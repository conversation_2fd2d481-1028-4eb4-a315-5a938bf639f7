<template>
  <div class="environment-chart-container">
    <!-- 环境数据网格布局展示 -->
    <div v-if="environmentData && environmentData.length > 0" class="environment-data-grid">
      <div 
        v-for="(item, index) in environmentData" 
        :key="index" 
        class="environment-data-cell"
        :class="{ 'warning': isWarningValue(item), 'critical': isCriticalValue(item) }"
      >
        <div class="env-icon-wrapper" :class="getStatusClass(item)">
          <i :class="getEnvironmentIcon(item.label)"></i>
        </div>
        <div class="env-info">
          <div class="env-value-line">
            <span class="env-value">{{ formatValue(item.value) }}</span>
            <span class="env-unit">{{ item.unit }}</span>
            <i v-if="getTrend(item) !== 'stable'" :class="getTrendIcon(item)" class="env-trend-icon" :title="getTrendTitle(item)"></i>
          </div>
          <div class="env-label">{{ item.label }}</div>
        </div>
      </div>
    </div>
    
    <!-- 无数据显示 -->
    <div v-else class="no-data-container">
      <i class="fas fa-wind"></i>
      <p>暂无环境数据</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnvironmentChart',
  props: {
    environmentData: {
      type: Array,
      required: true
    },
    deviceName: {
      type: String,
      default: '设备'
    }
  },
  data() {
    return {
      // 环境数据阈值配置
      thresholds: {
        '环境温度': { min: 0, max: 45, warning: 35, critical: 40 },
        '环境湿度': { min: 0, max: 100, warning: 75, critical: 85 },
        '环境风速': { min: 0, max: 100, warning: 60, critical: 80 },
        '环境风力': { min: 0, max: 12, warning: 6, critical: 8 },
        '环境风向': { min: 0, max: 360, warning: 360, critical: 360 },
        '环境噪声': { min: 0, max: 160, warning: 65, critical: 80 },
        '大气压力': { min: 0, max: 105, warning: 103, critical: 110 }
      },
      // 历史数据缓存，用于计算趋势
      historicalData: {},
      // 图标映射
      iconMap: {
        '环境温度': 'fas fa-thermometer-half',
        '环境湿度': 'fas fa-tint',
        '环境风速': 'fas fa-wind',
        '环境风力': 'fas fa-flag',
        '环境风向': 'fas fa-compass',
        '环境噪声': 'fas fa-volume-up',
        '大气压力': 'fas fa-tachometer-alt'
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 初始化历史数据
      this.initHistoricalData();
    });

    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize);
  },
  watch: {
    environmentData: {
      handler(newData, oldData) {
        // 更新历史数据以计算趋势
        this.updateHistoricalData(newData, oldData);
      },
      deep: true
    }
  },
  methods: {
    // 获取环境数据对应的图标
    getEnvironmentIcon(label) {
      return this.iconMap[label] || 'fas fa-chart-line';
    },
    
    // 获取状态样式类
    getStatusClass(item) {
      const value = parseFloat(item.value);
      const label = item.label;
      const threshold = this.thresholds[label] || { warning: 80, critical: 100 };
      
      if (value >= threshold.critical) {
        return 'status-critical';
      } else if (value >= threshold.warning) {
        return 'status-warning';
      }
      return 'status-normal';
    },
    
    // 格式化数值，保留小数点后一位
    formatValue(value) {
      const num = parseFloat(value);
      return isNaN(num) ? value : num.toFixed(1);
    },
    
    // 初始化历史数据
    initHistoricalData() {
      if (this.environmentData) {
        this.environmentData.forEach(item => {
          this.historicalData[item.label] = {
            previous: parseFloat(item.value),
            current: parseFloat(item.value),
            trend: 'stable'
          };
        });
      }
    },
    
    // 更新历史数据以计算趋势
    updateHistoricalData(newData, oldData) {
      if (!newData) return;
      
      newData.forEach(item => {
        const label = item.label;
        const currentValue = parseFloat(item.value);
        
        if (!this.historicalData[label]) {
          this.historicalData[label] = {
            previous: currentValue,
            current: currentValue,
            trend: 'stable'
          };
        } else {
          // 更新趋势
          const previousValue = this.historicalData[label].previous;
          this.historicalData[label].previous = previousValue;
          this.historicalData[label].current = currentValue;
          
          // 确定趋势方向
          if (currentValue > previousValue + 0.5) {
            this.historicalData[label].trend = 'up';
          } else if (currentValue < previousValue - 0.5) {
            this.historicalData[label].trend = 'down';
          } else {
            this.historicalData[label].trend = 'stable';
          }
        }
      });
    },
    
    // 获取趋势
    getTrend(item) {
      const label = item.label;
      return this.historicalData[label] ? this.historicalData[label].trend : 'stable';
    },
    
    // 获取趋势图标
    getTrendIcon(item) {
      const trend = this.getTrend(item);
      if (trend === 'up') {
        return 'fas fa-arrow-up';
      } else if (trend === 'down') {
        return 'fas fa-arrow-down';
      }
      return 'fas fa-minus';
    },
    
    // 获取趋势提示文本
    getTrendTitle(item) {
      const trend = this.getTrend(item);
      if (trend === 'up') {
        return '上升趋势';
      } else if (trend === 'down') {
        return '下降趋势';
      }
      return '保持稳定';
    },
    
    // 判断是否为警告值
    isWarningValue(item) {
      const label = item.label;
      const value = parseFloat(item.value);
      const threshold = this.thresholds[label] || { warning: 80, critical: 100 };
      
      return value >= threshold.warning && value < threshold.critical;
    },
    
    // 判断是否为危险值
    isCriticalValue(item) {
      const label = item.label;
      const value = parseFloat(item.value);
      const threshold = this.thresholds[label] || { critical: 100 };
      
      return value >= threshold.critical;
    },
    
    // 处理窗口大小变化
    handleResize() {
      // 可以在这里添加响应式调整逻辑
      console.log('窗口大小发生变化');
    }
  }
}
</script>

<style scoped>
.environment-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 3px 0; /* 增加上下内边距 */
  margin: 0;
  overflow: hidden;
  max-height: 85px; /* 略微增加容器高度 */
  background: transparent; /* 设置背景透明 */
}

.environment-data-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 4列布局 */
  grid-template-rows: repeat(2, 1fr); /* 2行布局 */
  gap: 4px; /* 增加单元格之间的间距 */
  width: 100%;
  padding: 2px; /* 增加内边距 */
  overflow-y: auto;
  max-height: 85px; /* 与容器最大高度一致 */
  scrollbar-width: none; /* 隐藏滚动条 */
  background: transparent; /* 设置背景透明 */
}

/* 隐藏WebKit浏览器的滚动条 */
.environment-data-grid::-webkit-scrollbar {
  display: none;
}

.environment-data-cell {
  background: rgba(0, 30, 60, 0.3); /* 降低背景不透明度 */
  border-radius: 3px; /* 增加圆角 */
  display: flex;
  align-items: center;
  gap: 4px; /* 增加内部元素间距 */
  padding: 3px 4px; /* 增加内边距 */
  position: relative;
  border: 1px solid rgba(0, 168, 255, 0.3); /* 增加边框亮度 */
  transition: all 0.3s ease;
  height: 32px; /* 增加单元格高度 */
  overflow: hidden;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.1); /* 添加发光效果 */
}

.environment-data-cell:hover {
  border-color: rgba(0, 168, 255, 0.7);
  box-shadow: 0 0 8px rgba(0, 168, 255, 0.3);
  transform: translateY(-1px);
}

.environment-data-cell.warning {
  border-color: rgba(255, 170, 0, 0.5);
  box-shadow: 0 0 5px rgba(255, 170, 0, 0.2);
}

.environment-data-cell.critical {
  border-color: rgba(255, 85, 85, 0.5);
  box-shadow: 0 0 5px rgba(255, 85, 85, 0.2);
  animation: cell-pulse 2s infinite;
}

@keyframes cell-pulse {
  0% { border-color: rgba(255, 85, 85, 0.5); box-shadow: 0 0 5px rgba(255, 85, 85, 0.2); }
  50% { border-color: rgba(255, 85, 85, 0.8); box-shadow: 0 0 8px rgba(255, 85, 85, 0.4); }
  100% { border-color: rgba(255, 85, 85, 0.5); box-shadow: 0 0 5px rgba(255, 85, 85, 0.2); }
}

.env-icon-wrapper {
  width: 24px; /* 增加图标容器尺寸 */
  height: 24px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  font-size: 0.85rem; /* 增加图标字体大小 */
  background-color: rgba(0, 168, 255, 0.15);
  color: #7fdbff;
  transition: all 0.3s ease;
  margin-left: 1px; /* 增加左边距 */
  box-shadow: 0 0 4px rgba(0, 168, 255, 0.3); /* 添加图标发光效果 */
}

.env-icon-wrapper.status-normal {
  background-color: rgba(0, 255, 157, 0.15);
  color: #00ff9d;
  box-shadow: 0 0 4px rgba(0, 255, 157, 0.3);
}

.env-icon-wrapper.status-warning {
  background-color: rgba(255, 170, 0, 0.15);
  color: #ffaa00;
  box-shadow: 0 0 4px rgba(255, 170, 0, 0.3);
}

.env-icon-wrapper.status-critical {
  background-color: rgba(255, 85, 85, 0.15);
  color: #ff5555;
  box-shadow: 0 0 4px rgba(255, 85, 85, 0.3);
  animation: icon-pulse 2s infinite;
}

@keyframes icon-pulse {
  0% { box-shadow: 0 0 4px rgba(255, 85, 85, 0.3); }
  50% { box-shadow: 0 0 8px rgba(255, 85, 85, 0.5); }
  100% { box-shadow: 0 0 4px rgba(255, 85, 85, 0.3); }
}

.env-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
  padding: 0 1px; /* 增加左右内边距 */
}

.env-value-line {
  display: flex;
  align-items: center;
  gap: 3px; /* 增加间距 */
  white-space: nowrap;
  margin-bottom: 2px; /* 增加值与标签之间的间距 */
}

.env-value {
  font-size: 0.8rem; /* 增加值字体大小 */
  color: white;
  font-weight: bold;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.5); /* 添加文字发光效果 */
}

.env-unit {
  font-size: 0.55rem; /* 增加单位字体大小 */
  color: rgba(255, 255, 255, 0.7);
  margin-top: -1px;
}

.env-label {
  font-size: 0.6rem; /* 增加标签字体大小 */
  color: rgba(127, 219, 255, 0.9);
  margin-top: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.env-trend-icon {
  font-size: 0.55rem; /* 减小趋势图标大小 */
  margin-left: 2px;
  animation: blink 2s infinite; /* 添加闪烁动画 */
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.fa-arrow-up {
  color: #ff5555;
}

.fa-arrow-down {
  color: #00ff9d;
}

.fa-minus {
  color: #ffff55;
}

.no-data-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 30px; /* 减小最小高度 */
  max-height: 100px;
  color: rgba(127, 219, 255, 0.6);
  background-color: rgba(0, 30, 60, 0.2); /* 降低背景不透明度 */
  border-radius: 4px;
  border: 1px dashed rgba(0, 168, 255, 0.3); /* 改为更科技感的边框 */
}

.no-data-container i {
  font-size: 1.5rem; /* 减小图标大小 */
  margin-bottom: 3px;
  animation: float 3s ease-in-out infinite; /* 添加浮动动画 */
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.no-data-container p {
  font-size: 0.7rem; /* 减小文字大小 */
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.5); /* 添加文字发光效果 */
}

/* 响应式调整 */
@media screen and (max-width: 1366px) {
  .environment-chart-container {
    max-height: 75px; /* 小屏幕下调整高度 */
    padding: 2px 0; /* 减小上下内边距 */
  }

  .environment-data-grid {
    max-height: 75px;
    gap: 3px; /* 小屏幕下减小间距 */
    padding: 1px; /* 减小内边距 */
  }
  
  .environment-data-cell {
    height: 30px; /* 小屏幕下调整单元格高度 */
    padding: 2px 3px;
    gap: 3px;
  }
  
  .env-icon-wrapper {
    width: 22px;
    height: 22px;
    font-size: 0.8rem;
  }
  
  .env-value-line {
    gap: 2px;
    margin-bottom: 1px;
  }
}

@media screen and (max-width: 1024px) {
  .environment-data-grid {
    grid-template-columns: repeat(2, 1fr); /* 小屏幕时改为2列 */
  }
}
</style>
