<template>
  <div class="right-column" style="display: block !important; visibility: visible !important; z-index: 500 !important; pointer-events: auto !important;">
    <!-- 项目信息小部件 (从左列移动过来) -->
    <div class="widget project-info">
      <div class="widget-header">
        <span><i class="fas fa-project-diagram"></i> 项目信息</span>
        <span class="widget-status"><i class="fas fa-circle"></i> 实时</span>
      </div>
      <div class="widget-content">
        <div class="project-stats">
          <div class="stat-item manager-stat">
            <div class="stat-label"><i class="fas fa-hashtag"></i> 项目编号</div>
            <div class="stat-value">{{ projectNumber }}</div>
            <div class="manager-info">
              <div class="manager-avatar" @click="showProjectInfo">
                <i class="fas fa-info-circle"></i>
              </div>
              <div class="manager-status online" title="项目状态">
                <i class="fas fa-circle"></i>
              </div>
            </div>
          </div>
          <div class="stat-item location-stat">
            <div class="stat-label"><i class="fas fa-map-marker-alt"></i> 项目位置</div>
            <div class="stat-value">{{ projectLocation }}</div>
            <div class="location-map">
              <div class="map-marker" title="点击查看详细位置" @click="showLocationMap">
                <i class="fas fa-map-marker-alt"></i>
              </div>
              <div class="map-coordinates">{{ projectCoordinates }}</div>
            </div>
          </div>

          <div class="stat-item equipment-stat">
            <div class="stat-label"><i class="fas fa-truck-monster"></i> 设备数量</div>
            <div class="stat-value">{{ equipmentCount }}台</div>
            <div class="equipment-icons" @click="showEquipmentList">
              <i class="fas fa-truck-monster"></i>
              <i class="fas fa-truck-monster"></i>
              <i class="fas fa-truck-monster"></i>
              <span class="equipment-more">+{{ equipmentCount > 3 ? equipmentCount - 3 : 0 }}</span>
            </div>
          </div>
          <div class="stat-item progress-stat">
            <div class="stat-label"><i class="fas fa-tasks"></i> 项目进度</div>
            <div class="stat-value">{{ (projectProgressValue || 0).toFixed(1) }}%</div>
            <div class="mini-progress-bar">
              <div class="mini-progress-fill" :style="{ width: (projectProgressValue || 0) + '%' }"></div>
            </div>
          </div>
          <div class="stat-item timeline-stat">
            <div class="stat-label"><i class="fas fa-calendar-alt"></i> 项目时间线</div>
            <div class="timeline-container">
              <div class="timeline-start">
                <div class="timeline-date">{{ startDate }}</div>
                <div class="timeline-label">开工</div>
              </div>
              <div class="timeline-progress">
                <div class="timeline-bar">
                  <div class="timeline-fill" :style="{ width: (projectProgressValue || 0) + '%' }"></div>
                </div>
                <div class="timeline-now" :style="{ left: (projectProgressValue || 0) + '%' }" title="当前进度" @click="showProgressDetails">
                  <i class="fas fa-caret-up"></i>
                </div>
              </div>
              <div class="timeline-end">
                <div class="timeline-date">{{ endDate }}</div>
                <div class="timeline-label">完工</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 监控小部件 -->
    <div class="widget monitoring">
      <div class="widget-header">
        <span><i class="fas fa-video"></i> 视频监控</span>
        <div class="widget-controls">
          <span class="page-indicator" v-if="cameras && cameras.length > 0">{{ currentPage + 1 }}/{{ totalPages || 1 }}</span>
          <span class="widget-status" v-else><i class="fas fa-exclamation-circle"></i> 无可用摄像头</span>
        </div>
      </div>
      <div class="widget-content">
        <div v-if="cameras && cameras.length > 0">
          <div class="device-selector">
            <button
              class="device-btn prev-btn"
              @click="prevDevice"
              :disabled="currentDeviceIndex === 0"
            >
              <i class="fas fa-chevron-left"></i>
            </button>
            <div class="device-title">
              <i class="fas fa-server"></i> {{ currentDeviceName }}
              <span class="device-camera-count" v-if="currentDeviceCameras.length > 0">
                ({{ currentDeviceCameras.length }}个摄像头)
              </span>
            </div>
            <button
              class="device-btn next-btn"
              @click="nextDevice"
              :disabled="currentDeviceIndex >= deviceNamesList.length - 1"
            >
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>

          <div class="monitoring-grid">
            <div
              v-for="camera in currentPageCameras"
              :key="camera.id"
              class="camera-feed"
              @click="showCameraDetail(camera)"
            >
              <div class="camera-label" v-html="camera.labelHtml"></div>
              <!-- 如果有URL，显示视频播放器；否则显示图片 -->
              <template v-if="camera.url && isActiveCamera(camera.id)">
                <div class="video-container" :id="`video-container-${camera.id}`">
                  <!-- Video.js 将在这里初始化 -->
                </div>
              </template>
              <template v-else-if="camera.hasError">
                <div class="video-error-container">
                  <img :src="camera.src" :alt="camera.alt" class="error-background">
                  <div class="error-message">
                    <!-- <i class="fas fa-exclamation-triangle"></i> -->
                    <p>{{ camera.errorMessage || '无可用视频源' }}</p>
                    <span class="error-details" v-if="camera.errorDetails">{{ camera.errorDetails }}</span>
                    <span class="error-details" v-else>{{ camera.status === 0 ? '摄像头离线或未连接' : '请检查摄像头配置和网络连接' }}</span>
                  </div>
                  <div class="retry-button" @click.stop="retryPlayVideo(camera)">
                    <i class="fas fa-sync-alt"></i> 重试
                  </div>
                </div>
              </template>
              <template v-else>
                <img :src="camera.src" :alt="camera.alt">
                <div class="play-button" v-if="camera.url" @click.stop="playVideo(camera)">
                  <i class="fas fa-play-circle"></i>
                </div>
                <div class="play-button error" v-else @click.stop="checkCameraUrl(camera)">
                  <i class="fas fa-exclamation-circle"></i>
                </div>
              </template>
              <div class="camera-status" v-if="camera.isRecording">
                <span class="rec-indicator">● REC</span>
              </div>
              <div class="camera-detail-button" @click.stop="showCameraDetail(camera)">
                <i class="fas fa-info-circle"></i>
              </div>
            </div>

            <!-- 如果当前页没有摄像头，显示提示信息 -->
            <div v-if="currentPageCameras.length === 0" class="no-cameras-message">
              <i class="fas fa-video-slash"></i>
              <p>该设备没有可用的摄像头</p>
            </div>
          </div>

          <div class="pagination-controls">
            <button
              class="page-btn prev-btn"
              @click="prevPage"
              :disabled="currentPage === 0"
            >
              <i class="fas fa-chevron-left"></i>
            </button>
            <div class="page-dots">
              <span
                v-for="(_, idx) in Array(totalPages)"
                :key="idx"
                :class="['page-dot', { active: currentPage === idx }]"
                @click="goToPage(idx)"
              ></span>
            </div>
            <button
              class="page-btn next-btn"
              @click="nextPage"
              :disabled="currentPage >= totalPages - 1"
            >
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
        <div v-else class="no-cameras-container">
          <div class="no-cameras-message full">
            <i class="fas fa-video-slash"></i>
            <p>未找到摄像头数据</p>
            <p class="no-camera-details">请检查API数据或设备连接状态</p>
          </div>
        </div>
      </div>
    </div>



    <!-- 摄像头详情弹窗 -->
    <camera-detail-modal
      :visible="showModal"
      :camera="selectedCamera"
      @close="closeModal"
      @status-change="handleCameraStatusChange"
    />
  </div>
</template>

<script>
import CameraDetailModal from './CameraDetailModal.vue'

export default {
  name: 'RightColumn',
  components: {
    CameraDetailModal
  },
  props: {
    projectId: {
      type: [Number, String],
      default: ''
    },
    selectedProject: {
      type: Object,
      default: () => null
    },
    deviceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentPage: 0,
      currentDeviceIndex: 0, // 当前显示的设备索引
      activeCameras: {}, // 当前激活的摄像头集合
      videoPlayers: {}, // 存储每个摄像头的播放器实例
      videoPlayer: null, // 兼容旧代码，保留此变量
      videoElement: null, // HTML5 video 元素
      camerasByDevice: {}, // 按设备分组的摄像头
      deviceNamesList: [], // 设备名称列表
      showModal: false, // 是否显示摄像头详情弹窗
      selectedCamera: null, // 当前选中的摄像头
      // 项目信息相关数据
      equipmentCount: 4,
      projectProgressValue: 42.5,
    }
  },
  computed: {
    // 项目数据相关计算属性
    projectData() {
      return this.selectedProject || {};
    },
    projectName() {
      return this.projectData.name || '未知项目';
    },
    projectDescription() {
      return this.projectData.description || '暂无描述';
    },
    projectLocation() {
      return this.projectData.location || '江苏南京';
    },
    projectNumber() {
      // 使用接口中的 number 字段作为项目号
      return this.projectData.number || '未知项目号';
    },
    projectCoordinates() {
      if (this.projectData.longitude && this.projectData.latitude) {
        // 确保经纬度是数字类型
        const lat = parseFloat(this.projectData.latitude);
        const lng = parseFloat(this.projectData.longitude);

        if (!isNaN(lat) && !isNaN(lng)) {
          return `${lat.toFixed(4)}° N, ${lng.toFixed(4)}° E`;
        }
      }
      return '32.0584° N, 118.7965° E';
    },
    startDate() {
      return this.projectData.start_date || '2024-01-01';
    },
    endDate() {
      return this.projectData.end_date || '2024-12-31';
    },
    // 从设备列表中提取摄像头信息
    cameras() {
      // 首先记录设备列表结构，用于调试
      console.log('获取摄像头信息，设备列表长度:', this.deviceList ? this.deviceList.length : 0);

      // 从设备列表中提取摄像头信息
      const extractedCameras = [];

      // 记录设备列表结构，用于调试
      if (this.deviceList && this.deviceList.length > 0) {
        console.log('设备列表结构:', JSON.stringify(this.deviceList.slice(0, 1)));
      } else {
        console.log('设备列表为空或未定义');
        return []; // 如果没有设备列表数据，返回空数组而不是使用默认数据
      }

      // 遍历设备列表
      this.deviceList.forEach((device, deviceIndex) => {
        const deviceName = device.name || device.device_name || `设备 ${deviceIndex + 1}`;
        console.log(`处理设备: ${deviceName}, 设备数据:`, device);

        // 检查设备是否有摄像头 - 兼容多种数据结构
        let deviceCameras = [];

        // 尝试从cameras字段获取
        if (device.cameras && Array.isArray(device.cameras) && device.cameras.length > 0) {
          deviceCameras = device.cameras;
          console.log(`设备 ${deviceName} 的cameras字段有 ${deviceCameras.length} 个摄像头`);
        }
        // 尝试从camera字段获取
        else if (device.camera && Array.isArray(device.camera) && device.camera.length > 0) {
          deviceCameras = device.camera;
          console.log(`设备 ${deviceName} 的camera字段有 ${deviceCameras.length} 个摄像头`);
        }
        // 尝试从devices字段获取
        else if (device.devices && Array.isArray(device.devices) && device.devices.length > 0) {
          // 对于设备组，尝试从子设备获取摄像头
          device.devices.forEach((subDevice, subIndex) => {
            const subDeviceName = subDevice.name || subDevice.device_name || `${deviceName}-子设备${subIndex + 1}`;
            if (subDevice.cameras && Array.isArray(subDevice.cameras) && subDevice.cameras.length > 0) {
              console.log(`子设备 ${subDeviceName} 有 ${subDevice.cameras.length} 个摄像头`);
              subDevice.cameras.forEach(cam => {
                deviceCameras.push({
                  ...cam,
                  deviceName: subDeviceName,
                  parentDeviceId: device.id
                });
              });
            }
          });
          console.log(`设备组 ${deviceName} 的子设备总共有 ${deviceCameras.length} 个摄像头`);
        }
        // 设备本身可能是一个摄像头
        else if (device.type === 'camera' || device.device_type === 'camera' || device.id) {
          console.log(`设备 ${deviceName} 本身是一个摄像头`);
          // 创建一个摄像头对象
          deviceCameras = [{
            id: device.id || `camera-${deviceIndex}`,
            name: device.name || device.device_name || `摄像头 ${deviceIndex + 1}`,
            url: device.url || device.stream_url || device.rtsp_url || device.ezviz_url || '',
            thumbnail_url: device.thumbnail_url || device.thumbnail || '',
            status: device.status || 1,
            is_recording: device.is_recording || false
          }];
        }

        if (deviceCameras.length === 0) {
          console.log(`设备 ${deviceName} 没有摄像头或摄像头列表无效`);
          // 如果设备没有摄像头，跳过该设备而不是创建默认摄像头
          return;
        }

        // 遍历摄像头列表
        deviceCameras.forEach((camera, cameraIndex) => {
          // 提取所有可能的URL
          const ezvizUrl = camera.ezviz_url || ''; // 萤石云URL
          const streamUrl = camera.stream_url || '';
          const rtspUrl = camera.rtsp_url || '';
          const origUrl = camera.orig_url || '';
          const cameraUrl = camera.url || '';

          // 使用第一个可用的URL
          const url = ezvizUrl || streamUrl || rtspUrl || origUrl || cameraUrl || '';

          // 生成摄像头描述HTML
          const cameraName = camera.name || camera.camera_name || `摄像头 ${cameraIndex + 1}`;
          const labelHtml = `<div class="camera-name">${cameraName}</div><div class="camera-device">${deviceName}</div>`;

          // 构造摄像头对象
          const cameraObj = {
            id: camera.id || `camera-${deviceIndex}-${cameraIndex}`,
            deviceId: device.id || `device-${deviceIndex}`,
            deviceName: deviceName,
            name: cameraName,
            labelHtml: labelHtml,
            url: url,
            src: camera.thumbnail_url || camera.thumbnail || '/assets/images/video-placeholder.jpg',
            alt: `${deviceName} ${cameraName} 预览图`,
            status: camera.status || 1,
            isOnline: camera.status !== 0,
            hasError: false,
            errorMessage: '',
            errorDetails: '',
            isRecording: camera.is_recording || false,
            position: camera.position || null,
            type: camera.type || 'normal',
            device: device // 保存设备引用，方便后续操作
          };

          // 将摄像头添加到提取列表
          extractedCameras.push(cameraObj);
          console.log(`添加摄像头: ${cameraName}, URL: ${url.substring(0, 30)}...`);
        });
      });

      console.log(`从设备列表中提取了 ${extractedCameras.length} 个摄像头`);
      return extractedCameras;
    },
    // 按设备分组的摄像头
    cameraGroups() {
      if (!this.cameras || this.cameras.length === 0) {
        return {};
      }

      // 按设备名称分组
      const groups = {};
      this.cameras.forEach(camera => {
        const deviceName = camera.deviceName;
        if (!groups[deviceName]) {
          groups[deviceName] = [];
        }
        groups[deviceName].push(camera);
      });

      // 更新实例属性，方便其他方法使用
      this.camerasByDevice = groups;
      this.deviceNamesList = Object.keys(groups);

      return groups;
    },
    // 当前设备名称
    currentDeviceName() {
      if (this.deviceNamesList.length === 0) {
        return '无设备';
      }

      // 确保索引不超出范围
      const safeIndex = Math.min(this.currentDeviceIndex, this.deviceNamesList.length - 1);
      return this.deviceNamesList[safeIndex] || '未知设备';
    },
    // 当前设备的摄像头
    currentDeviceCameras() {
      const deviceName = this.currentDeviceName;
      if (deviceName === '无设备') {
        return [];
      }
      return this.camerasByDevice[deviceName] || [];
    },
    // 总页数
    totalPages() {
      const camerasPerPage = 4; // 每页显示4个摄像头
      return Math.max(1, Math.ceil(this.currentDeviceCameras.length / camerasPerPage));
    },
    // 当前页的摄像头
    currentPageCameras() {
      if (this.currentDeviceCameras.length === 0) {
        return [];
      }
      const camerasPerPage = 4; // 每页显示4个摄像头
      const startIndex = this.currentPage * camerasPerPage;
      const endIndex = startIndex + camerasPerPage;
      return this.currentDeviceCameras.slice(startIndex, endIndex);
    },

  },
  watch: {
    deviceList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          console.log('设备列表已更新，共 ' + newVal.length + ' 个设备');
        }
      },
      immediate: true
    },
    // 监听当前页摄像头变化
    currentPageCameras: {
      handler(newVal) {
        if (newVal && newVal.length > 0 && !this.activeCamera) {
          console.log('当前页摄像头已更新，尝试自动播放');
          // 延迟执行，确保DOM已更新
          this.$nextTick(() => {
            this.autoPlayFirstAvailableCamera();
          });
        }
      },
      immediate: false
    }
  },
  mounted() {
    console.log('RightColumn 组件已挂载');

    // 初始化cameraGroups，确保设备列表正确
    this.$nextTick(() => {
      // 如果有摄像头数据但没有设备列表，生成设备列表
      if (this.cameras && this.cameras.length > 0) {
        console.log('有摄像头数据，确保设备列表正确初始化');

        // 强制计算cameraGroups，初始化设备列表
        const groups = this.cameraGroups;
        if (Object.keys(groups).length > 0) {
          console.log('设备列表初始化成功，共有 ' + Object.keys(groups).length + ' 个设备');
        } else {
          console.log('没有设备可用，请检查API返回的设备数据');
        }

        // 如果当前设备索引超出范围，重置为0
        if (this.currentDeviceIndex >= this.deviceNamesList.length) {
          console.log('当前设备索引超出范围，重置为0');
          this.currentDeviceIndex = 0;
        }
      } else {
        console.log('没有摄像头数据，请检查API返回数据');
      }
    });

    // 自动加载并播放第一个可用的摄像头视频
    setTimeout(() => {
      console.log('延迟调用自动播放摄像头');
      if (this.cameras && this.cameras.length > 0) {
        this.autoPlayFirstAvailableCamera();
      } else {
        console.log('没有可用的摄像头数据，无法自动播放');
      }
    }, 3000); // 延迟3秒，确保其他组件已加载完成
  },

  beforeDestroy() {
    // 销毁视频播放器
    this.destroyVideoPlayer();
  },

  // 检查 Video.js 是否可用
  created() {
    // 检查全局 videojs 对象
    if (typeof window.videojs !== 'undefined') {
      console.log('Video.js 全局可用 (window.videojs)');
      // 确保 videojs 变量可用
      if (typeof videojs === 'undefined') {
        window.videojs = window.videojs;
      }
    } else {
      console.warn('Video.js 未在全局范围内定义，视频播放可能不可用');
      // 尝试从其他地方获取
      try {
        // 尝试从 window 对象的其他属性获取
        const possibleNames = ['videojs', 'VideoJS', 'vjs', 'videoJs'];
        for (const name of possibleNames) {
          if (window[name]) {
            window.videojs = window[name];
            console.log(`从 window.${name} 获取 videojs 成功`);
            break;
          }
        }
      } catch (error) {
        console.error('尝试获取 videojs 失败:', error);
      }
    }

    // 最终检查
    if (typeof window.videojs === 'undefined') {
      console.error('无法获取 videojs，视频播放将不可用。请刷新页面重试。');
    }
  },
  methods: {
    // 切换到上一个设备
    prevDevice() {
      if (this.currentDeviceIndex > 0) {
        console.log(`切换到上一个设备: 从索引 ${this.currentDeviceIndex} 到 ${this.currentDeviceIndex - 1}`);
        this.currentDeviceIndex--;
        this.currentPage = 0; // 重置页码

        // 设备切换后自动播放
        this.$nextTick(() => {
          // 如果当前设备没有摄像头，显示提示信息
          if (!this.currentDeviceCameras || this.currentDeviceCameras.length === 0) {
            console.warn(`设备 ${this.currentDeviceName} 没有摄像头`);
          } else {
            console.log(`设备 ${this.currentDeviceName} 有 ${this.currentDeviceCameras.length} 个摄像头`);
            this.autoPlayFirstAvailableCamera();
          }
        });
      } else {
        console.log('已经是第一个设备，无法切换到上一个');
      }
    },

    // 切换到下一个设备
    nextDevice() {
      if (this.currentDeviceIndex < this.deviceNamesList.length - 1) {
        console.log(`切换到下一个设备: 从索引 ${this.currentDeviceIndex} 到 ${this.currentDeviceIndex + 1}`);
        this.currentDeviceIndex++;
        this.currentPage = 0; // 重置页码

        // 设备切换后自动播放
        this.$nextTick(() => {
          // 如果当前设备没有摄像头，显示提示信息
          if (!this.currentDeviceCameras || this.currentDeviceCameras.length === 0) {
            console.warn(`设备 ${this.currentDeviceName} 没有摄像头`);
          } else {
            console.log(`设备 ${this.currentDeviceName} 有 ${this.currentDeviceCameras.length} 个摄像头`);
            this.autoPlayFirstAvailableCamera();
          }
        });
      } else {
        console.log('已经是最后一个设备，无法切换到下一个');
      }
    },

    // 切换到上一页（左侧）
    prevPage() {
      if (this.currentPage > 0) {
        this.currentPage--;
        // 页面切换后自动播放
        this.$nextTick(() => {
          this.autoPlayFirstAvailableCamera();
        });
      }
    },

    // 切换到下一页（右侧）
    nextPage() {
      if (this.currentPage < this.totalPages - 1) {
        this.currentPage++;
        // 页面切换后自动播放
        this.$nextTick(() => {
          this.autoPlayFirstAvailableCamera();
        });
      }
    },

    // 跳转到指定页
    goToPage(pageIndex) {
      if (pageIndex >= 0 && pageIndex <= this.totalPages - 1) {
        this.currentPage = pageIndex;
        // 页面切换后自动播放
        this.$nextTick(() => {
          this.autoPlayFirstAvailableCamera();
        });
      }
    },

    startDataSimulation() {
      // 每5秒更新一次数据
      const simulationTimer = setInterval(() => {
        try {
          // 随机更新摄像头录制状态
          if (Math.random() > 0.85) {
            const randomIndex = Math.floor(Math.random() * this.cameras.length);
            const camera = this.cameras[randomIndex];

            // 切换录制状态
            camera.isRecording = !camera.isRecording;

            // 更新标签显示
            if (camera.isRecording) {
              camera.labelHtml = camera.label + ' <span style="color:#ff5555">● 录制中</span>';
            } else {
              camera.labelHtml = camera.label;
            }

            // 短暂延迟后重置录制状态
            if (camera.isRecording) {
              setTimeout(() => {
                camera.isRecording = false;
                camera.labelHtml = camera.label;
              }, 5000);
            }
          }

          // 不再使用模拟数据更新姿态水平状态
        } catch (error) {
          console.error('数据模拟更新失败:', error);
        }
      }, 5000);

      // 保存定时器ID以便在组件销毁时清除
      this.simulationTimer = simulationTimer;
    },



















    // 检查摄像头是否激活
    isActiveCamera(cameraId) {
      return this.activeCameras[cameraId] === true;
    },

    // 检查摄像头URL
    checkCameraUrl(camera) {
      console.log('检查摄像头URL:', camera);

      // 如果摄像头没有URL，尝试使用备用URL
      if (!camera.url) {
        if (camera.ezviz_url || camera.orig_url || camera.rtspUrl || camera.stream_url) {
          console.log('找到备用URL:', camera.ezviz_url || camera.orig_url || camera.rtspUrl || camera.stream_url);
          camera.url = camera.ezviz_url || camera.orig_url || camera.rtspUrl || camera.stream_url;
          // 清除错误状态
          camera.hasError = false;
          camera.errorMessage = '';
          // 尝试播放视频
          this.playVideo(camera);
        } else {
          // 设置错误状态
          camera.hasError = true;
          camera.errorMessage = '无可用视频源';
          console.error('摄像头没有可用的URL:', camera.label);
        }
      } else {
        // 尝试播放视频
        this.playVideo(camera);
      }
    },

    // 重试播放视频
    retryPlayVideo(camera) {
      console.log('重试播放视频:', camera);

      // 清除错误状态
      camera.hasError = false;
      camera.errorMessage = '';

      // 重新检查URL
      this.checkCameraUrl(camera);
    },

    // 播放视频
    async playVideo(camera) {
      console.log('尝试播放视频:', camera);

      // 如果没有摄像头对象，直接返回
      if (!camera) {
        console.warn('摄像头对象为空，无法播放视频');
        return;
      }

      // 如果已经在播放这个摄像头，则停止播放
      if (this.isActiveCamera(camera.id)) {
        console.log('停止播放当前摄像头:', camera.id);
        this.destroyVideoPlayer(camera.id);
        this.$set(this.activeCameras, camera.id, false);
        return;
      }

      // 确保摄像头有可用的URL
      if (!camera.url) {
        console.warn('摄像头没有主URL，尝试使用备用URL');
        // 尝试使用 ezviz_url、orig_url 或 rtspUrl
        if (camera.ezviz_url || camera.orig_url || camera.rtspUrl || camera.stream_url) {
          console.log('找到备用URL:', camera.ezviz_url || camera.orig_url || camera.rtspUrl || camera.stream_url);
          camera.url = camera.ezviz_url || camera.orig_url || camera.rtspUrl || camera.stream_url;
        } else {
          // 设置错误状态
          console.error('摄像头没有任何可用的URL:', camera.label);
          camera.hasError = true;
          camera.errorMessage = '无可用视频源';
          camera.errorDetails = '请检查摄像头配置或网络连接';
          // 强制更新视图以显示错误状态
          this.$forceUpdate();
          return;
        }
      }

      // 设置当前激活的摄像头
      this.$set(this.activeCameras, camera.id, true);
      console.log('设置当前激活的摄像头:', camera.id, '播放URL:', camera.url);

      // 等待 DOM 更新
      this.$nextTick(async () => {
        try {
          // 获取视频容器
          let videoContainer = document.getElementById(`video-container-${camera.id}`);
          if (!videoContainer) {
            console.warn(`首次查找失败，视频容器不存在: video-container-${camera.id}`);

            // 尝试多次查找容器，最多重试3次
            let retryCount = 0;
            const maxRetries = 3;
            const retryDelay = 200; // 每次重试间隔200ms

            const findContainer = () => {
              return new Promise((resolve) => {
                const tryFind = () => {
                  const container = document.getElementById(`video-container-${camera.id}`);
                  if (container) {
                    console.log(`第 ${retryCount + 1} 次重试找到视频容器: video-container-${camera.id}`);
                    resolve(container);
                  } else if (retryCount < maxRetries) {
                    retryCount++;
                    console.log(`第 ${retryCount} 次重试查找视频容器: video-container-${camera.id}`);
                    setTimeout(tryFind, retryDelay);
                  } else {
                    console.error(`重试 ${maxRetries} 次后仍找不到视频容器: video-container-${camera.id}`);
                    resolve(null);
                  }
                };
                tryFind();
              });
            };

            videoContainer = await findContainer();

            if (!videoContainer) {
              console.error('延迟后仍找不到视频容器，无法播放视频');
              // 如果找不到容器，取消激活状态并设置错误状态
              this.$set(this.activeCameras, camera.id, false);
              camera.hasError = true;
              camera.errorMessage = '视频容器初始化失败';
              camera.errorDetails = '请刷新页面重试';
              this.$forceUpdate();
              return;
            }
          }

          console.log('视频容器尺寸:', videoContainer.clientWidth, 'x', videoContainer.clientHeight);
          this.initVideoPlayer(camera, videoContainer);
        } catch (error) {
          console.error('初始化视频播放器失败:', error);
          // 如果初始化失败，取消激活状态
          this.$set(this.activeCameras, camera.id, false);
        }
      });
    },

    // 初始化视频播放器
    initVideoPlayer(camera, videoContainer) {
      try {
        // 清空容器
        videoContainer.innerHTML = '';

        // 创建视频元素
        const videoElement = document.createElement('video');
        videoElement.className = 'video-js vjs-default-skin';
        videoElement.id = `video-player-${camera.id}`;
        videoElement.controls = true;
        videoElement.preload = 'auto';
        videoElement.width = videoContainer.clientWidth || 160;
        videoElement.height = videoContainer.clientHeight || 120;

        // 添加到容器
        videoContainer.appendChild(videoElement);

        // 确保video.js已加载
        if (typeof window.videojs === 'undefined') {
          console.error('Video.js not loaded!');
        //  alert('视频播放器未加载，请刷新页面重试');
          // 设置错误状态
          camera.hasError = true;
          camera.errorMessage = '视频播放器未加载';
          // 取消激活状态
          this.$set(this.activeCameras, camera.id, false);
          return;
        }

        // 确保摄像头有URL
        if (!camera.url) {
          console.error('摄像头没有URL');
          // 设置错误状态
          camera.hasError = true;
          camera.errorMessage = '无可用视频源';
          // 取消激活状态
          this.$set(this.activeCameras, camera.id, false);
          return;
        }

        // 确保本地 videojs 变量可用
        const videojs = window.videojs;

        // 确定视频类型
        let videoType = this.getVideoType(camera.url);

        console.log('初始化播放器，URL:', camera.url, '类型:', videoType);

        // 创建播放器实例
        try {
          // 确保之前的播放器已被销毁
          const cameraId = camera.id.toString();
          if (this.videoPlayers[cameraId]) {
            try {
              const oldPlayer = this.videoPlayers[cameraId];
              if (typeof oldPlayer.isDisposed === 'function' && !oldPlayer.isDisposed()) {
                oldPlayer.dispose();
              }
              this.videoPlayers[cameraId] = null;
            } catch (e) {
              console.warn(`销毁摄像头 ${cameraId} 的旧播放器失败:`, e);
              this.videoPlayers[cameraId] = null;
            }
          }

          // 创建新播放器
          const player = videojs(`video-player-${camera.id}`, {
            sources: [{
              src: camera.url,
              type: videoType
            }],
            autoplay: true,
            muted: true, // 默认静音，避免自动播放策略限制
            controls: true,
            fluid: true,
            preload: 'auto',
            responsive: true,
            html5: {
              vhs: {
                overrideNative: true
              }
            },
            // 添加错误处理选项
            errorDisplay: true,
            loadingSpinner: true,
            // 禁用一些可能导致问题的功能
            liveui: false,
            liveTracker: false
          });

          // 存储播放器实例
          this.videoPlayers[cameraId] = player;

          // 兼容旧代码，设置 videoPlayer 变量
          this.videoPlayer = player;

          // 添加错误处理
          if (player && typeof player.on === 'function') {
            player.on('error', () => {
              console.warn(`摄像头 ${cameraId} 的播放器发生错误`);
            });
          }
        } catch (e) {
          console.error('创建播放器实例失败:', e);
          camera.hasError = true;
          camera.errorMessage = '创建播放器失败';
          camera.errorDetails = e.message || '未知错误';
          this.$set(this.activeCameras, camera.id, false);
          return;
        }

        // 创建一个变量来跟踪是否已经尝试过备用URL
        let hasTriedBackup = false;

        // 监听错误事件
        this.videoPlayer.on('error', (error) => {
          console.error('视频播放错误:', error);

          // 如果已经尝试过备用URL，不再重复尝试
          if (hasTriedBackup) {
            this.handleVideoError(camera, '视频源无法播放');
            return;
          }

          hasTriedBackup = true; // 标记已尝试备用URL

          // 尝试使用备用URL
          if (camera.ezviz_url && camera.ezviz_url !== camera.url) {
            console.log('尝试使用萤石云URL:', camera.ezviz_url);
            try {
              if (this.videoPlayer && !this.videoPlayer.isDisposed()) {
                this.videoPlayer.src([{
                  src: camera.ezviz_url,
                  type: this.getVideoType(camera.ezviz_url)
                }]);
                this.videoPlayer.play().catch(e => {
                  console.error('萤石云URL播放失败:', e);
                  this.tryNextBackupUrl(camera);
                });
              }
            } catch (e) {
              console.error('设置萤石云URL失败:', e);
              this.tryNextBackupUrl(camera);
            }
          } else {
            this.tryNextBackupUrl(camera);
          }
        });

        // 添加一个超时检查，如果视频长时间没有播放，显示错误信息
        const playTimeout = setTimeout(() => {
          if (this.videoPlayer && !this.videoPlayer.isDisposed() && !this.videoPlayer.hasStarted_) {
            console.error('视频播放超时');
            this.handleVideoError(camera, '视频加载超时，请检查网络连接');
          }
        }, 15000); // 15秒超时

        // 监听播放开始事件，清除超时检查
        this.videoPlayer.on('playing', () => {
          clearTimeout(playTimeout);
        });

        // 监听播放就绪事件
        this.videoPlayer.on('ready', () => {
          console.log('视频播放器就绪');

          // 在小屏幕上，可能需要手动调整播放器大小
          if (window.innerWidth <= 768) {
            try {
              // 延迟执行，确保视频元素已加载
              setTimeout(() => {
                if (this.videoPlayer && !this.videoPlayer.isDisposed() &&
                    videoContainer && videoContainer.clientWidth && videoContainer.clientHeight) {
                  // 安全地调整尺寸
                  try {
                    this.videoPlayer.dimensions(videoContainer.clientWidth, videoContainer.clientHeight);
                  } catch (e) {
                    console.warn('调整播放器尺寸失败:', e);
                  }
                }
              }, 300);
            } catch (e) {
              console.warn('准备调整播放器尺寸失败:', e);
            }
          }
        });

        // 监听播放开始事件
        this.videoPlayer.on('play', () => {
          console.log('视频开始播放');

          // 添加静音提示和点击取消静音功能
          try {
            if (this.videoPlayer && !this.videoPlayer.isDisposed()) {
              // 安全地检查静音状态
              try {
                const isMuted = this.videoPlayer.muted();
                if (isMuted) {
                  // 如果需要显示静音按钮，取消注释下面的代码
                  // this.addUnmuteButton(videoContainer);
                }
              } catch (e) {
                console.warn('检查静音状态失败:', e);
              }
            }
          } catch (e) {
            console.warn('播放事件处理失败:', e);
          }
        });

        // 移除重复的错误事件监听器，避免冲突

        // 创建一个安全的尺寸调整函数
        const safeResizePlayer = () => {
          try {
            // 检查播放器是否存在且未被销毁
            if (!this.videoPlayer || typeof this.videoPlayer.isDisposed !== 'function') {
              return;
            }

            if (this.videoPlayer.isDisposed()) {
              return;
            }

            // 检查容器是否存在且有有效尺寸
            if (!videoContainer || !videoContainer.clientWidth || !videoContainer.clientHeight) {
              return;
            }

            // 检查播放器的tech元素是否存在
            let tech;
            try {
              tech = this.videoPlayer.tech({ IWillNotUseThisInPlugins: true });
            } catch (techError) {
              console.warn('获取播放器tech元素失败:', techError);
              return;
            }

            // 检查tech元素是否有效
            if (!tech || !tech.el_) {
              return;
            }

            // 检查videoWidth属性是否存在
            try {
              // 使用一个临时变量检查videoWidth是否可访问
              const videoWidth = tech.el_.videoWidth;
              if (videoWidth === null || videoWidth === undefined) {
                return;
              }

              // 安全地调整尺寸
              this.videoPlayer.dimensions(videoContainer.clientWidth, videoContainer.clientHeight);
            } catch (dimensionError) {
              console.warn('获取视频宽度或调整尺寸失败:', dimensionError);
            }
          } catch (e) {
            console.warn('调整播放器尺寸失败:', e);
          }
        };

        // 使用防抖函数处理resize事件，避免频繁调用
        let resizeTimeout;
        const debouncedResize = () => {
          clearTimeout(resizeTimeout);
          resizeTimeout = setTimeout(safeResizePlayer, 200);
        };

        // 监听窗口大小变化，调整播放器大小
        window.addEventListener('resize', debouncedResize);
      } catch (error) {
        console.error('初始化视频播放器失败:', error);
      }
    },

    // 销毁视频播放器
    destroyVideoPlayer(cameraId) {
      if (cameraId) {
        // 销毁特定摄像头的播放器
        const cameraIdStr = cameraId.toString();
        const playerId = `video-player-${cameraId}`;

        // 首先尝试从 videoPlayers 对象中获取播放器
        if (this.videoPlayers[cameraIdStr]) {
          try {
            const player = this.videoPlayers[cameraIdStr];
            if (player && typeof player.isDisposed === 'function' && !player.isDisposed()) {
              // 先暂停播放，避免继续触发事件
              try { player.pause(); } catch (e) {}

              // 销毁播放器
              player.dispose();
              console.log(`销毁摄像头 ${cameraId} 的播放器`);
            }
            // 清除引用
            this.videoPlayers[cameraIdStr] = null;
          } catch (error) {
            console.error(`销毁摄像头 ${cameraId} 的播放器失败:`, error);
            this.videoPlayers[cameraIdStr] = null;
          }
        }
        // 如果在 videoPlayers 中没有找到，尝试从全局 getPlayers 中获取
        else if (window.videojs && window.videojs.getPlayers && window.videojs.getPlayers()[playerId]) {
          try {
            const player = window.videojs.getPlayers()[playerId];
            if (player && typeof player.isDisposed === 'function' && !player.isDisposed()) {
              // 先暂停播放，避免继续触发事件
              try { player.pause(); } catch (e) {}

              // 销毁播放器
              player.dispose();
              console.log(`销毁摄像头 ${cameraId} 的播放器 (从全局获取)`);
            }
          } catch (error) {
            console.error(`销毁摄像头 ${cameraId} 的播放器失败 (从全局获取):`, error);
          }
        }

        // 清除DOM中可能残留的视频元素
        try {
          const container = document.getElementById(`video-container-${cameraId}`);
          if (container) {
            container.innerHTML = '';
          }
        } catch (e) {
          console.error('清除视频容器失败:', e);
        }
      } else {
        // 销毁所有播放器
        try {
          // 销毁 videoPlayers 中的所有播放器
          Object.keys(this.videoPlayers).forEach(id => {
            const player = this.videoPlayers[id];
            if (player && typeof player.isDisposed === 'function' && !player.isDisposed()) {
              try { player.pause(); } catch (e) {}
              player.dispose();
              console.log(`销毁摄像头 ${id} 的播放器`);
            }
            this.videoPlayers[id] = null;
          });

          // 兼容旧代码，销毁 videoPlayer
          if (this.videoPlayer && typeof this.videoPlayer.isDisposed === 'function' && !this.videoPlayer.isDisposed()) {
            try { this.videoPlayer.pause(); } catch (e) {}
            this.videoPlayer.dispose();
          }
          this.videoPlayer = null;
          console.log('销毁所有播放器');
        } catch (error) {
          console.error('销毁播放器失败:', error);
          // 确保引用被清除
          this.videoPlayer = null;
          this.videoPlayers = {};
        }
      }
    },



    // 显示摄像头详情弹窗
    showCameraDetail(camera) {
      console.log('显示摄像头详情:', camera);
      this.selectedCamera = camera;
      this.showModal = true;

      // 在小屏幕上，停止当前播放的视频以节省资源
      if (window.innerWidth <= 768) {
        // 停止所有播放的视频
        Object.keys(this.activeCameras).forEach(cameraId => {
          if (this.activeCameras[cameraId]) {
            this.destroyVideoPlayer(cameraId);
            this.$set(this.activeCameras, cameraId, false);
          }
        });
      }
      // 在大屏幕上，保持视频播放
    },

    // 关闭摄像头详情弹窗
    closeModal() {
      console.log('关闭摄像头详情弹窗');
      this.showModal = false;
      this.selectedCamera = null;

      // 在小屏幕上，恢复视频播放
      if (window.innerWidth <= 768) {
        // 延迟执行，确保DOM已更新
        this.$nextTick(() => {
          this.autoPlayFirstAvailableCamera();
        });
      }
    },

    // 自动播放第一个摄像头视频（不检查URL）
    autoPlayFirstCamera() {
      console.log('尝试自动播放第一个摄像头视频');

      // 检查是否有摄像头
      if (!this.currentPageCameras || this.currentPageCameras.length === 0) {
        console.warn('没有可用的摄像头，无法自动播放');
        return;
      }

      // 获取第一个摄像头
      const firstCamera = this.currentPageCameras[0];

      // 检查摄像头是否有URL
      if (!firstCamera.url) {
        console.warn('第一个摄像头没有URL，无法自动播放');
        return;
      }

      console.log('自动播放第一个摄像头:', firstCamera.label);

      // 播放视频
      this.playVideo(firstCamera);
    },

    // 自动播放第一个可用的摄像头视频（检查URL）
    autoPlayFirstAvailableCamera() {
      console.log('尝试自动播放可用摄像头视频');

      // 检查是否有摄像头
      if (!this.currentPageCameras || this.currentPageCameras.length === 0) {
        console.warn('当前页没有可用的摄像头，无法自动播放');

        // 如果当前设备没有摄像头，但有其他设备有摄像头，尝试切换设备
        if (this.deviceNamesList.length > 1 && !this.currentDeviceCameras.length) {
          console.log('当前设备没有摄像头，尝试切换到其他设备');

          // 查找有摄像头的设备
          for (let i = 0; i < this.deviceNamesList.length; i++) {
            if (i !== this.currentDeviceIndex) {
              const deviceName = this.deviceNamesList[i];
              const cameras = this.camerasByDevice[deviceName] || [];
              if (cameras.length > 0) {
                console.log(`找到有摄像头的设备: ${deviceName}，切换到该设备`);
                this.currentDeviceIndex = i;
                this.currentPage = 0;

                // 延迟执行，确保DOM已更新
                this.$nextTick(() => {
                  this.autoPlayFirstAvailableCamera();
                });
                return;
              }
            }
          }
        }

        return;
      }

      // 清除所有激活的摄像头
      Object.keys(this.activeCameras).forEach(cameraId => {
        this.destroyVideoPlayer(cameraId);
        this.$set(this.activeCameras, cameraId, false);
      });

      // 在小屏幕上，只播放第一个摄像头
      if (window.innerWidth <= 768) {
        // 查找第一个有URL的摄像头
        const availableCamera = this.currentPageCameras.find(camera => {
          return camera.url || camera.ezviz_url || camera.rtspUrl || camera.stream_url || camera.orig_url;
        });

        if (!availableCamera) {
          console.warn('没有找到可用的摄像头URL，无法自动播放');

          // 如果有摄像头但没有URL，设置所有摄像头为错误状态
          this.currentPageCameras.forEach(camera => {
            camera.hasError = true;
            camera.errorMessage = '无可用视频源';
            camera.errorDetails = '请检查摄像头配置或网络连接';
          });

          // 强制更新视图以显示错误状态
          this.$forceUpdate();
          return;
        }

        console.log('小屏幕模式：自动播放第一个可用摄像头:', availableCamera.name);

        // 确保摄像头有URL
        if (!availableCamera.url && (availableCamera.ezviz_url || availableCamera.rtspUrl || availableCamera.stream_url || availableCamera.orig_url)) {
          availableCamera.url = availableCamera.ezviz_url || availableCamera.rtspUrl || availableCamera.stream_url || availableCamera.orig_url;
        } else if (!availableCamera.url) {
          // 如果仍然没有URL，设置错误状态
          availableCamera.hasError = true;
          availableCamera.errorMessage = '无可用视频源';
          availableCamera.errorDetails = '请检查摄像头配置或网络连接';
          // 使用警告级别而不是错误级别，因为这可能是正常情况
          console.warn('摄像头没有可用的URL:', availableCamera.name);
          // 强制更新视图以显示错误状态
          this.$forceUpdate();
          return;
        }

        // 播放视频
        this.playVideo(availableCamera);
      } else {
        // 在大屏幕上，播放所有摄像头
        console.log('大屏幕模式：尝试播放所有可用摄像头');

        // 查找所有有URL的摄像头
        const availableCameras = this.currentPageCameras.filter(camera => {
          return camera.url || camera.ezviz_url || camera.rtspUrl || camera.stream_url || camera.orig_url;
        });

        if (availableCameras.length === 0) {
          console.warn('没有找到可用的摄像头URL，无法自动播放');

          // 如果有摄像头但没有URL，设置所有摄像头为错误状态
          this.currentPageCameras.forEach(camera => {
            camera.hasError = true;
            camera.errorMessage = '无可用视频源';
            camera.errorDetails = '请检查摄像头配置或网络连接';
          });

          // 强制更新视图以显示错误状态
          this.$forceUpdate();
          return;
        }

        // 最多播放4个摄像头
        const maxCameras = Math.min(availableCameras.length, 4);
        console.log(`找到 ${availableCameras.length} 个可用摄像头，将播放 ${maxCameras} 个`);

        // 播放视频，使用延迟播放避免同时创建多个播放器导致性能问题
        for (let i = 0; i < maxCameras; i++) {
          const camera = availableCameras[i];

          // 确保摄像头有URL
          if (!camera.url && (camera.ezviz_url || camera.rtspUrl || camera.stream_url || camera.orig_url)) {
            camera.url = camera.ezviz_url || camera.rtspUrl || camera.stream_url || camera.orig_url;
          }

          // 使用延迟播放，每个摄像头延迟300毫秒
          const delay = i * 300;
          console.log(`播放第 ${i+1} 个摄像头: ${camera.name}，延迟 ${delay}ms`);

          setTimeout(() => {
            // 再次检查URL是否有效
            if (camera.url) {
              // 在播放前检查是否已经有激活的摄像头，避免重复播放
              if (!this.isActiveCamera(camera.id)) {
                console.log(`延迟播放摄像头: ${camera.name}`);
                this.playVideo(camera);
              } else {
                console.log(`摄像头 ${camera.name} 已经在播放，跳过`);
              }
            } else {
              console.warn(`摄像头 ${camera.name} 没有有效的URL，跳过播放`);
              camera.hasError = true;
              camera.errorMessage = '无可用视频源';
              camera.errorDetails = '请检查摄像头配置或网络连接';
              // 强制更新视图以显示错误状态
              this.$forceUpdate();
            }
          }, delay);
        }
      }
    },

    // 添加取消静音按钮
    addUnmuteButton(container) {
      // 检查是否已经有取消静音按钮
      if (container.querySelector('.unmute-button')) {
        return;
      }

      // 创建取消静音按钮
      const unmuteButton = document.createElement('div');
      unmuteButton.className = 'unmute-button';
      unmuteButton.innerHTML = '<i class="fas fa-volume-mute"></i> 点击取消静音';
      unmuteButton.style.position = 'absolute';
      unmuteButton.style.bottom = '60px';
      unmuteButton.style.left = '50%';
      unmuteButton.style.transform = 'translateX(-50%)';
      unmuteButton.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
      unmuteButton.style.color = 'white';
      unmuteButton.style.padding = '5px 10px';
      unmuteButton.style.borderRadius = '4px';
      unmuteButton.style.cursor = 'pointer';
      unmuteButton.style.zIndex = '10';
      unmuteButton.style.fontSize = '12px';

      // 添加点击事件
      unmuteButton.addEventListener('click', () => {
        try {
          // 尝试找到容器对应的摄像头ID
          const cameraIdMatch = container.id.match(/video-container-(\d+)/);
          if (cameraIdMatch && cameraIdMatch[1]) {
            const cameraId = cameraIdMatch[1];
            const player = this.videoPlayers[cameraId];

            if (player && typeof player.isDisposed === 'function' && !player.isDisposed()) {
              player.muted(false);
              unmuteButton.remove();
              return;
            }
          }

          // 如果无法找到特定播放器，尝试使用当前播放器
          if (this.videoPlayer && typeof this.videoPlayer.isDisposed === 'function' && !this.videoPlayer.isDisposed()) {
            this.videoPlayer.muted(false);
            unmuteButton.remove();
          } else {
            // 如果播放器不存在或已销毁，直接移除按钮
            unmuteButton.remove();
          }
        } catch (e) {
          console.warn('取消静音失败:', e);
          // 出错时也移除按钮
          unmuteButton.remove();
        }
      });

      // 添加到容器
      container.appendChild(unmuteButton);

      // 5秒后自动隐藏
      setTimeout(() => {
        if (unmuteButton.parentNode) {
          unmuteButton.style.opacity = '0.5';
        }
      }, 5000);
    },

    // 尝试下一个备用URL
    tryNextBackupUrl(camera) {
      const cameraId = camera.id.toString();
      const player = this.videoPlayers[cameraId];

      if (!player || (typeof player.isDisposed === 'function' && player.isDisposed())) {
        this.handleVideoError(camera, '视频播放器已销毁，无法尝试备用URL');
        return;
      }

      if (camera.rtspUrl && camera.rtspUrl !== camera.url) {
        console.log(`尝试为摄像头 ${camera.label} 使用备用RTSP URL:`, camera.rtspUrl);
        try {
          player.src([{
            src: camera.rtspUrl,
            type: this.getVideoType(camera.rtspUrl)
          }]);
          player.play().catch(e => {
            console.error(`摄像头 ${camera.label} 的RTSP URL播放失败:`, e);
            this.tryLastBackupUrl(camera);
          });
        } catch (e) {
          console.error(`设置摄像头 ${camera.label} 的RTSP URL失败:`, e);
          this.tryLastBackupUrl(camera);
        }
      } else {
        this.tryLastBackupUrl(camera);
      }
    },

    // 尝试最后一个备用URL
    tryLastBackupUrl(camera) {
      const cameraId = camera.id.toString();
      const player = this.videoPlayers[cameraId];

      if (!player || (typeof player.isDisposed === 'function' && player.isDisposed())) {
        this.handleVideoError(camera, '视频播放器已销毁，无法尝试备用URL');
        return;
      }

      if (camera.stream_url && camera.stream_url !== camera.url) {
        console.log(`尝试为摄像头 ${camera.label} 使用备用流URL:`, camera.stream_url);
        try {
          player.src([{
            src: camera.stream_url,
            type: this.getVideoType(camera.stream_url)
          }]);
          player.play().catch(e => {
            console.error(`摄像头 ${camera.label} 的备用流URL播放失败:`, e);
            this.handleVideoError(camera, '所有视频源均无法播放');
          });
        } catch (e) {
          console.error(`设置摄像头 ${camera.label} 的备用流URL失败:`, e);
          this.handleVideoError(camera, '所有视频源均无法播放');
        }
      } else {
        this.handleVideoError(camera, '所有视频源均无法播放');
      }
    },

    // 处理视频错误
    handleVideoError(camera, errorMessage) {
      console.error(errorMessage);

      // 获取摄像头ID
      const cameraId = camera.id.toString();

      // 安全地销毁播放器
      if (this.videoPlayers[cameraId]) {
        try {
          const player = this.videoPlayers[cameraId];
          // 先移除所有事件监听器，避免触发额外的错误
          if (player && typeof player.isDisposed === 'function' && !player.isDisposed()) {
            // 暂停播放
            try { player.pause(); } catch (e) {}

            // 销毁播放器
            player.dispose();
          }
        } catch (e) {
          console.error(`销毁摄像头 ${cameraId} 的播放器失败:`, e);
        } finally {
          // 确保播放器引用被清除
          this.videoPlayers[cameraId] = null;
        }
      }

      // 兼容旧代码，处理 videoPlayer
      if (this.videoPlayer && this.videoPlayer.id_ === `video-player-${cameraId}`) {
        try {
          if (typeof this.videoPlayer.isDisposed === 'function' && !this.videoPlayer.isDisposed()) {
            try { this.videoPlayer.pause(); } catch (e) {}
            this.videoPlayer.dispose();
          }
        } catch (e) {
          console.error('销毁当前播放器失败:', e);
        } finally {
          this.videoPlayer = null;
        }
      }

      // 设置错误状态
      camera.hasError = true;
      camera.errorMessage = errorMessage || '视频源无法播放';
      camera.errorDetails = '请检查摄像头状态和网络连接';

      // 取消激活状态
      this.$set(this.activeCameras, camera.id, false);

      // 更新DOM
      this.$forceUpdate();
    },

    // 处理摄像头状态变化
    handleCameraStatusChange(statusData) {
      console.log('摄像头状态变化:', statusData);

      // 查找对应的摄像头并更新状态
      const camera = this.cameras.find(c => c.id.toString() === statusData.id.toString());
      if (camera) {
        // 更新状态
        camera.status = statusData.status;
        camera.hasError = statusData.status === 2 || statusData.status === 0;
        camera.errorMessage = statusData.message || (statusData.status === 0 ? '摄像头离线' : '摄像头故障');

        // 如果是当前选中的摄像头，更新选中的摄像头对象
        if (this.selectedCamera && this.selectedCamera.id.toString() === statusData.id.toString()) {
          this.selectedCamera = { ...camera };
        }

        // 如果摄像头正在播放，停止播放
        if (this.activeCameras[camera.id] && camera.hasError) {
          this.destroyVideoPlayer(camera.id);
          this.$set(this.activeCameras, camera.id, false);
        }

        // 强制更新视图
        this.$forceUpdate();
      }
    },

    // 重试播放视频
    retryPlayVideo(camera) {
      console.log('重试播放视频:', camera);

      // 重置错误状态
      camera.hasError = false;
      camera.errorMessage = '';

      // 尝试播放视频
      this.playVideo(camera);
    },

    // 检查摄像头URL
    checkCameraUrl(camera) {
      console.log('检查摄像头URL:', camera);

      // 如果没有URL，尝试从其他字段获取
      if (!camera.url && (camera.ezviz_url || camera.rtspUrl || camera.stream_url || camera.orig_url)) {
        camera.url = camera.ezviz_url || camera.rtspUrl || camera.stream_url || camera.orig_url;
        // 尝试播放视频
        this.playVideo(camera);
      } else {
        // 设置错误状态
        camera.hasError = true;
        camera.errorMessage = '无可用视频源';
        // 强制更新视图
        this.$forceUpdate();
      }
    },

    // 根据URL获取视频类型
    getVideoType(url) {
      if (!url) return 'video/mp4';

      const lowerUrl = url.toLowerCase();

      // 萤石云URL特殊处理
      if (lowerUrl.includes('open.ys7.com') || lowerUrl.includes('ezopen://')) {
        return 'application/x-mpegURL';
      }

      // 根据URL后缀判断视频类型
      if (lowerUrl.endsWith('.mp4')) {
        return 'video/mp4';
      } else if (lowerUrl.endsWith('.webm')) {
        return 'video/webm';
      } else if (lowerUrl.endsWith('.ogg') || lowerUrl.endsWith('.ogv')) {
        return 'video/ogg';
      } else if (lowerUrl.includes('.m3u8')) {
        return 'application/x-mpegURL';
      } else if (lowerUrl.includes('rtsp://')) {
        return 'application/x-rtsp';
      } else if (lowerUrl.includes('rtmp://')) {
        return 'rtmp/mp4';
      } else {
        // 如果没有明确的扩展名，尝试根据URL中的其他信息判断
        if (lowerUrl.includes('live') || lowerUrl.includes('stream') || lowerUrl.includes('hls') ||
            lowerUrl.includes('openlive')) {
          return 'application/x-mpegURL'; // 可能是HLS流
        } else {
          return 'video/mp4'; // 默认为MP4
        }
      }
    },

    // 项目信息相关方法
    showProjectInfo() {
      const projectNumber = this.projectNumber;
      const projectName = this.projectName;
      const projectStatus = this.projectData.status === 1 ? '进行中' : '筹备中';
      const createdAt = this.projectData.created_at || '未知';
      const updatedAt = this.projectData.updated_at || '未知';

      // 显示项目基本信息
      alert(`项目编号：${projectNumber}\n项目名称：${projectName}\n项目状态：${projectStatus}\n创建时间：${createdAt}\n更新时间：${updatedAt}`);
    },

    showLocationMap() {
      const location = this.projectLocation;
      const coordinates = this.projectCoordinates;

      alert(`将打开项目位置地图：${location}\n坐标：${coordinates}`);
    },

    showProgressDetails() {
      const progress = (this.projectProgressValue || 0).toFixed(1);

      // 接口中没有 completed_tasks 字段，我们可以根据项目名称和描述生成一些任务
      let tasks = [];

      // 使用项目描述中的内容作为任务
      if (this.projectData.description) {
        // 尝试按分隔符拆分描述为多个任务
        const descParts = this.projectData.description.split(/[,，、;；.。]/);
        tasks = descParts.filter(part => part.trim().length > 0);
      }

      // 如果没有足够的任务，添加一些默认任务
      if (tasks.length < 2) {
        const defaultTasks = [
          '桥墩基础施工',
          '首跨梁段安装',
          '第二跨梁段吊装',
          '第三跨梁段安装'
        ];

        // 只添加缺少的任务数量
        const tasksToAdd = defaultTasks.slice(0, Math.max(2 - tasks.length, 0));
        tasks = [...tasks, ...tasksToAdd];
      }

      // 根据进度计算已完成的任务数量
      const completedTaskCount = Math.max(1, Math.floor((tasks.length * progress) / 100));

      // 生成已完成任务文本
      const completedTasks = tasks.slice(0, completedTaskCount);

      alert(`项目进度：${progress}%\n已完成任务：\n${completedTasks.join('\n')}`);
    },

    showEquipmentList() {
      alert(`设备总数：${this.equipmentCount}台\n\n设备列表:\n1. 主桥塔\n2. 辅助塔\n3. 监控站\n4. 移动工作台`);
    },
  }
}
</script>

<style scoped>
.right-column {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: visible;
  padding: 0;
  margin: 0;
  padding-top: 0;
  margin-top: 0;
  width: 380px;
  min-width: 380px;
  max-width: 380px;
  z-index: 300 !important; /* 确保在3D模型上方 */
  pointer-events: auto !important; /* 确保可以接收鼠标事件 */
  box-sizing: border-box;
  position: relative; /* 确保相对定位 */
  right: 0; /* 确保靠右对齐 */
  gap: 2px; /* 进一步减小模块间距 */
}

/* 全屏模式下的样式调整 */
:deep(.fullscreen-mode) .right-column,
:deep(.fullscreen-active) .right-column,
:deep(body.fullscreen-mode) .right-column,
:deep(html.fullscreen-mode) .right-column {
  display: flex !important;
  visibility: visible !important;
  z-index: 1000 !important;
  pointer-events: auto !important;
}

/* 导入右侧列样式 */
@import '../../../../assets/css/right-column.css';
/* 导入漂浮模块样式 */
@import '../../../../assets/css/floating-modules.css';

/* 调整右侧列模块间距 */
.widget {
  margin: 0 0 2px 0 !important; /* 只保留2px的底部间距 */
  padding: 0 !important;
}

/* 调整右侧列模块内容边距 */
.widget-content {
  margin: 0 !important;
  padding: 3px !important; /* 只保留3px的内边距 */
}

/* 调整右侧列模块头部边距 */
.widget-header {
  margin: 0 !important;
  padding: 2px 5px !important; /* 减小头部内边距 */
  height: 24px !important; /* 减小头部高度 */
  line-height: 24px !important; /* 调整行高与高度一致 */
  font-size: 12px !important; /* 减小字体大小 */
}

/* 调整监控网格间距 */
.monitoring-grid {
  gap: 5px !important; /* 减小网格间距 */
  margin: 0 !important;
  padding: 0 !important;
}

/* 调整摄像头标签边距 */
.camera-label {
  margin: 0 !important;
  padding: 2px !important;
  font-size: 10px !important; /* 减小字体大小 */
}

/* 调整分页控件边距 */
.pagination-controls {
  margin: 2px 0 0 0 !important;
  padding: 0 !important;
}

/* 调整图表容器边距 */
.chart-container {
  margin: 0 !important;
  padding: 0 !important;
  min-height: 180px !important; /* 减小最小高度 */
  z-index: 300 !important;
  pointer-events: auto !important;
  visibility: visible !important;
}

/* 视频容器样式 */
.video-container {
  width: 100%;
  height: 100%;
  min-height: 120px;
  min-width: 160px;
  background-color: #000;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 播放按钮样式 */
.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.8);
  font-size: 3rem;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.play-button:hover {
  color: #fff;
  transform: translate(-50%, -50%) scale(1.1);
}

.play-button.error {
  color: #ff5555;
  animation: pulse-error 2s infinite;
}

@keyframes pulse-error {
  0% { opacity: 0.7; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
  100% { opacity: 0.7; transform: translate(-50%, -50%) scale(1); }
}

.video-error-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  overflow: hidden;
}

.error-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.3;
  filter: blur(2px);
}

.error-message {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
  text-align: center;
  z-index: 2;
}

.error-message i {
  font-size: 24px;
  color: #ff5555;
  margin-bottom: 10px;
}

.error-message p {
  color: #fff;
  font-size: 14px;
  margin: 0 0 3px 0;
  font-weight: bold;
}

.error-message .error-details {
  color: #ff9999;
  font-size: 11px;
  display: block;
  margin-top: 3px;
  max-width: 90%;
  text-align: center;
}

.retry-button {
  position: relative;
  margin-top: 10px;
  padding: 5px 10px;
  background-color: rgba(0, 40, 80, 0.7);
  color: #00a8ff;
  border: 1px solid rgba(0, 168, 255, 0.5);
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  z-index: 2;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.retry-button:hover {
  background-color: rgba(0, 60, 100, 0.9);
  border-color: #00a8ff;
  transform: scale(1.05);
}

/* 摄像头样式 */
.camera-feed {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.camera-feed:hover {
  transform: scale(1.02);
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* Video.js 自定义样式 */
.video-js {
  width: 100% !important;
  height: 100% !important;
  min-height: 120px !important;
  min-width: 160px !important;
  background-color: #000 !important;
}

.vjs-default-skin {
  color: #00a8ff;
}

.vjs-default-skin .vjs-big-play-button {
  background-color: rgba(0, 40, 80, 0.7);
  border-color: #00a8ff;
}

.vjs-default-skin .vjs-control-bar {
  background-color: rgba(0, 40, 80, 0.7);
}

.vjs-default-skin .vjs-slider {
  background-color: rgba(0, 168, 255, 0.3);
}

.vjs-default-skin .vjs-play-progress,
.vjs-default-skin .vjs-volume-level {
  background-color: #00a8ff;
}

/* 设备选择器样式 */
.device-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 0 5px;
}

.device-btn {
  background-color: rgba(0, 40, 80, 0.5);
  color: #00a8ff;
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.device-btn:hover:not(:disabled) {
  background-color: rgba(0, 40, 80, 0.8);
  border-color: rgba(0, 168, 255, 0.6);
}

.device-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.device-title {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #00a8ff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-camera-count {
  font-size: 12px;
  color: rgba(0, 168, 255, 0.7);
  margin-left: 5px;
}

/* 无摄像头提示样式 */
.no-cameras-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
  grid-column: span 2;
}

.no-cameras-message i {
  font-size: 48px;
  margin-bottom: 10px;
  color: rgba(255, 85, 85, 0.7);
}

/* 摄像头状态样式 */
.camera-status {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 3px 6px;
  border-radius: 4px;
  font-size: 12px;
}

/* 摄像头详情按钮样式 */
.camera-detail-button {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 40, 80, 0.7);
  color: #00a8ff;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 168, 255, 0.5);
  z-index: 10;
}

.camera-detail-button:hover {
  opacity: 1;
  background-color: rgba(0, 40, 80, 0.9);
  transform: scale(1.1);
  border-color: #00a8ff;
}

/* 右侧列通用样式优化 */
.right-column .widget {
  background: transparent !important; /* 设置所有模块背景透明 */
  border: none !important; /* 移除边框 */
  box-shadow: none !important; /* 移除阴影 */
  margin-bottom: 3px; /* 减小模块间距 */
}

.right-column .widget-header {
  background: transparent !important; /* 设置所有模块头部背景透明 */
  border-bottom: 1px solid rgba(0, 168, 255, 0.2) !important; /* 添加底部边框增强科技感 */
  height: 22px !important; /* 统一头部高度 */
  line-height: 22px !important; /* 统一行高 */
  margin: 0 !important;
  padding: 2px 5px !important; /* 减小头部内边距 */
  font-size: 12px !important; /* 减小字体大小 */
}

.right-column .widget-content {
  background: transparent !important; /* 设置所有模块内容区域背景透明 */
  padding: 3px !important; /* 统一内边距 */
}

/* 报警信息模块科技感样式 */
.widget.alarm-info {
  background: transparent !important;
}

.widget.alarm-info .widget-content {
  background: transparent !important;
}

/* 报警卡片样式优化 */
.alarm-card {
  background: rgba(0, 30, 60, 0.3) !important;
  border: 1px solid rgba(0, 168, 255, 0.3) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.1) !important;
  transition: all 0.3s ease;
}

.alarm-card:hover {
  transform: translateY(-2px);
  border-color: rgba(0, 168, 255, 0.7) !important;
  box-shadow: 0 0 8px rgba(0, 168, 255, 0.3) !important;
}

.alarm-card.unprocessed {
  border-color: rgba(255, 85, 85, 0.5) !important;
  box-shadow: 0 0 5px rgba(255, 85, 85, 0.2) !important;
  animation: alarm-pulse 2s infinite;
}

@keyframes alarm-pulse {
  0% { border-color: rgba(255, 85, 85, 0.5); box-shadow: 0 0 5px rgba(255, 85, 85, 0.2); }
  50% { border-color: rgba(255, 85, 85, 0.8); box-shadow: 0 0 8px rgba(255, 85, 85, 0.4); }
  100% { border-color: rgba(255, 85, 85, 0.5); box-shadow: 0 0 5px rgba(255, 85, 85, 0.2); }
}

/* 监控模块科技感样式 */
.widget.monitoring {
  background: transparent !important;
}

.widget.monitoring .widget-content {
  background: transparent !important;
}

/* 设备选择器样式 */
.device-selector {
  background: rgba(0, 30, 60, 0.3) !important;
  border: 1px solid rgba(0, 168, 255, 0.3) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.1) !important;
  border-radius: 3px;
  margin-bottom: 4px;
}

.device-title {
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
}

/* 监控网格样式 */
.monitoring-grid {
  gap: 5px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}

.camera-feed {
  background: rgba(0, 30, 60, 0.3) !important;
  border: 1px solid rgba(0, 168, 255, 0.3) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.1) !important;
  border-radius: 3px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.camera-feed:hover {
  transform: translateY(-2px);
  border-color: rgba(0, 168, 255, 0.7) !important;
  box-shadow: 0 0 8px rgba(0, 168, 255, 0.3) !important;
}

.camera-label {
  background: rgba(0, 30, 60, 0.5) !important;
  border-bottom: 1px solid rgba(0, 168, 255, 0.3) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
}

.video-container {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.play-button {
  color: rgba(0, 168, 255, 0.8) !important;
  text-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
}

.play-button:hover {
  color: rgba(0, 168, 255, 1) !important;
  text-shadow: 0 0 15px rgba(0, 168, 255, 0.8);
}

.play-button.error {
  color: rgba(255, 85, 85, 0.8) !important;
  text-shadow: 0 0 10px rgba(255, 85, 85, 0.5);
}

.camera-status .rec-indicator {
  color: #ff5555;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 分页控件样式 */
.pagination-controls {
  margin: 4px 0 0 0 !important;
  padding: 0 !important;
  background: rgba(0, 30, 60, 0.3) !important;
  border: 1px solid rgba(0, 168, 255, 0.3) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.1) !important;
  border-radius: 3px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-btn {
  background: rgba(0, 30, 60, 0.5) !important;
  border: 1px solid rgba(0, 168, 255, 0.3) !important;
  color: rgba(0, 168, 255, 0.8) !important;
}

.page-btn:hover:not(:disabled) {
  background: rgba(0, 40, 80, 0.7) !important;
  border-color: rgba(0, 168, 255, 0.7) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.3) !important;
}

.page-dot {
  background: rgba(0, 168, 255, 0.3) !important;
  box-shadow: 0 0 3px rgba(0, 168, 255, 0.2) !important;
}

.page-dot.active {
  background: rgba(0, 168, 255, 0.8) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.5) !important;
}

/* 姿态水平状态模块科技感样式 */
.widget.attitude-status {
  background: transparent !important;
}

.widget.attitude-status .widget-content {
  background: transparent !important;
}

.chart-container {
  background-color: rgba(0, 20, 40, 0.3) !important;
  border: 1px solid rgba(0, 168, 255, 0.3) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.1) !important;
}

/* 无摄像头消息样式 */
.no-cameras-message {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: rgba(127, 219, 255, 0.6);
  background-color: rgba(0, 30, 60, 0.2);
  border-radius: 4px;
  border: 1px dashed rgba(0, 168, 255, 0.3);
  grid-column: span 2;
}

.no-cameras-message i {
  font-size: 2rem;
  margin-bottom: 8px;
  animation: float 3s ease-in-out infinite;
}

.no-cameras-message p {
  font-size: 0.8rem;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.no-cameras-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

.no-cameras-message.full {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 280px;
  color: rgba(127, 219, 255, 0.6);
  background-color: rgba(0, 30, 60, 0.2);
  border-radius: 4px;
  border: 1px dashed rgba(0, 168, 255, 0.3);
  margin: 10px;
}

.no-cameras-message.full i {
  font-size: 3rem;
  margin-bottom: 10px;
  animation: float 3s ease-in-out infinite;
  color: rgba(255, 85, 85, 0.7);
}

.no-cameras-message.full p {
  font-size: 1rem;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
  margin: 5px 0;
}

.no-cameras-message.full .no-camera-details {
  font-size: 0.8rem;
  color: rgba(127, 219, 255, 0.4);
  margin-top: 10px;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}
</style>

