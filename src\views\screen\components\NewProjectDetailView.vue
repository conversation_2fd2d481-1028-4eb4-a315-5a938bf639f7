<template>

    <div class="dashboard-container transparent-container" ref="dashboardContainer" style="position: relative !important; width: 100% !important; height: 100% !important; margin: 0 !important; padding: 0 !important;">

      <div v-if="currentView === 'dashboard'" class="center-column-container" ref="centerColumnContainer" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0; z-index: 50; background-color: transparent !important; pointer-events: auto !important; touch-action: auto !important; visibility: visible !important; backdrop-filter: none !important;">
           <center-column :projectId="projectId" :selectedProject="selectedProject" />
      </div>

      <div class="header-container" style="height: 50px !important; margin: 0 !important; padding: 0 !important; z-index: 200 !important; position: absolute !important; top: 0 !important; left: 0 !important; width: 100% !important; overflow: visible !important; pointer-events: none !important;">
        <div style="pointer-events: auto !important;">
          <dashboard-header
            @back-to-list="goBackToMap"
            @toggle-fullscreen="toggleFullScreen"
            @nav-change="handleNavChange"
            :project-data="projectData || selectedProject"
          />
        </div>
      </div>

      <div class="main-content" ref="mainContent" style="top: 50px !important; height: calc(100% - 50px) !important; position: absolute !important; margin: 0 !important; padding: 0 !important;  width: 100% !important; left: 0 !important; z-index: 300 !important; touch-action: auto !important; gap: 0 !important; pointer-events: none !important; visibility: visible !important;">


        <template v-if="currentView === 'dashboard'">

          <div class="left-column-wrapper" style="position: absolute !important; left: 0 !important; top: 25px !important; height: 100% !important; z-index: 900 !important; margin: 0 !important; padding: 0 !important; pointer-events: auto !important; visibility: visible !important; display: block !important; width: 380px !important; background-color: rgba(0, 10, 30, 0.2) !important; backdrop-filter: blur(10px) !important; border-right: 1px solid rgba(0, 168, 255, 0.1) !important;">
            <left-column :projectId="projectId" :selectedProject="selectedProject" :deviceList="deviceList" />
          </div>

          <div class="right-column-wrapper" style="position: absolute !important; right: 0 !important; top: 25px !important; height: 100% !important; z-index: 900 !important; margin: 0 !important; padding: 0 !important; pointer-events: auto !important; visibility: visible !important; display: block !important; width: 380px !important; background-color: rgba(0, 10, 30, 0.2) !important; backdrop-filter: blur(10px) !important; border-left: 1px solid rgba(0, 168, 255, 0.1) !important;">
            <right-column :projectId="projectId" :selectedProject="selectedProject" :deviceList="deviceList" />
          </div>
        </template>


        <template v-if="currentView === 'device-management'">
          <div class="device-management-fullscreen" style="position: absolute !important; left: 0 !important; top: 25px !important; width: 100% !important; height: 100% !important; z-index: 1000 !important; margin: 0 !important; padding: 0 !important; pointer-events: auto !important; visibility: visible !important; display: block !important; box-sizing: border-box; background: transparent;">
            <device-management
              :projectId="projectId"
              :selectedProject="selectedProject"
              @close="closeDeviceManagement"
            />
          </div>
        </template>


        <template v-if="currentView === 'camera-management'">
          <div class="camera-management-fullscreen" style="position: absolute !important; left: 0 !important; top: 25px !important; width: 100% !important; height: 100% !important; z-index: 1000 !important; margin: 0 !important; padding: 0 !important; pointer-events: auto !important; visibility: visible !important; display: block !important; box-sizing: border-box; background: transparent;">
            <camera-management
              :projectId="projectId"
              :selectedProject="selectedProject"
              @close="closeCameraManagement"
            />
          </div>
        </template>
      </div>
    </div>
</template>

<script>
import DashboardHeader from './newScreen/DashboardHeader.vue'
import LeftColumn from './newScreen/LeftColumn.vue'
import CenterColumn from './newScreen/CenterColumn.vue'
import RightColumn from './newScreen/RightColumn.vue'
import DeviceManagement from './newScreen/DeviceManagement.vue'
import CameraManagement from './newScreen/CameraManagement.vue'
import { getProjectDetail } from '@/api/project'

export default {
  name: 'NewProjectDetailView',
  components: {
    DashboardHeader,
    LeftColumn,
    CenterColumn,
    RightColumn,
    DeviceManagement,
    CameraManagement
  },
  props: {
    projectId: {
      type: [Number, String],
      default: ''
    },
    selectedProject: {
      type: Object,
      default: () => null
    },
    currentTime: {
      type: String,
      default: ''
    },
    isFullscreen: {
      type: Boolean,
      default: false
    },
    deviceList: {
      type: Array,
      default: () => []
    },
    modelView: {
      type: String,
      default: '3d'
    }
  },
  data() {
    return {
      isFullscreenMode: false,
      isHeaderVisible: true,
      projectData: null,
      loading: false,
      currentView: 'dashboard' // 当前视图模式：'dashboard' 或 'device-management'
    }
  },
  watch: {
    projectId: {
      handler(newVal) {
        if (newVal) {
          this.fetchProjectDetail();
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 加载自动缩放脚本
    // this.loadAutoScaleScript();

    // 立即应用样式
    this.$nextTick(() => {
      console.log('*******应用样式');
      // 确保透明容器背景透明，直接使用大屏背景
      const transparentContainer = document.querySelector('.transparent-container');
      if (transparentContainer) {
        transparentContainer.style.backgroundColor = 'transparent';
        transparentContainer.style.backdropFilter = 'none';
        transparentContainer.style.borderRadius = '0'; // 移除圆角
        transparentContainer.style.boxShadow = 'none'; // 移除阴影
        transparentContainer.style.zIndex = '100';
      }

      // 确保父容器背景透明
      const parentContainer = document.querySelector('.screen-container');
      if (parentContainer) {
        parentContainer.style.backgroundColor = 'transparent';
      }

      // 确保dashboard容器在地图上方显示
      const dashboard = this.$refs.dashboardContainer;
      if (dashboard) {
        dashboard.style.zIndex = '100';
      }
    });

    // 监听全屏变化事件
    document.addEventListener('fullscreenchange', this.handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange);

    // 添加窗口大小变化监听，确保样式一致性
    window.addEventListener('resize', this.handleResize);
  },

  beforeDestroy() {
    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize);

    // 移除全屏变化事件监听器
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange);

    // 如果处于全屏状态，退出全屏
    if (document.fullscreenElement) {
      this.exitFullscreen();
    }
  },
  methods: {
    // 获取项目详情
    async fetchProjectDetail() {
      if (!this.projectId) return;

      try {
        this.loading = true;
        const response = await getProjectDetail(this.projectId);

        if (response && response.code === 0) {
          console.log('获取项目详情成功:', response.data);
          this.projectData = response.data;
        } else {
          console.error('获取项目详情失败:', response.message || '未知错误');
        }
      } catch (error) {
        console.error('获取项目详情异常:', error);
      } finally {
        this.loading = false;
      }
    },

    // 处理导航变化
    handleNavChange(navData) {
      console.log('导航变化:', navData);

      if (navData.index === 0) {
        // 综合展示
        this.currentView = 'dashboard';
      } else if (navData.index === 1) {
        // 设备管理
        this.currentView = 'device-management';
      } else if (navData.index === 2) {
        // 摄像头管理
        this.currentView = 'camera-management';
      }
    },

    // 关闭设备管理
    closeDeviceManagement() {
      this.currentView = 'dashboard';
    },

    // 关闭摄像头管理
    closeCameraManagement() {
      this.currentView = 'dashboard';
    },

    // 返回地图视图
    goBackToMap() {
      console.log('返回地图操作触发');
      // 修改事件名称为父组件监听的事件 back-to-list
      this.$emit('back-to-list');
    },

    // 切换全屏模式 - 参考src/views/screen/index.vue的实现
    toggleFullScreen() {
      console.log('切换全屏模式，当前状态:', this.isFullscreenMode ? '全屏' : '非全屏');

      if (!document.fullscreenElement &&
          !document.mozFullScreenElement &&
          !document.webkitFullscreenElement &&
          !document.msFullscreenElement) {
        // 进入全屏模式
        const element = this.$refs.projectDetailContainer || document.documentElement;
        if (!element) {
          console.error('找不到容器元素，无法进入全屏模式');
          return;
        }

        console.log('尝试进入全屏模式');
        try {
          let fullscreenPromise;
          if (element.requestFullscreen) {
            fullscreenPromise = element.requestFullscreen();
          } else if (element.mozRequestFullScreen) {
            fullscreenPromise = element.mozRequestFullScreen();
          } else if (element.webkitRequestFullscreen) {
            fullscreenPromise = element.webkitRequestFullscreen();
          } else if (element.msRequestFullscreen) {
            fullscreenPromise = element.msRequestFullscreen();
          }

          // 处理Promise（如果支持）
          if (fullscreenPromise && typeof fullscreenPromise.then === 'function') {
            fullscreenPromise
              .then(() => {
                console.log('全屏模式启用成功');
                this.applyFullscreenStyles();
              })
              .catch(error => {
                console.error('全屏模式启用失败:', error);
                // 如果全屏失败，仍然应用全屏样式
                this.applyFullscreenStyles();
              });
          } else {
            // 对于不支持Promise的浏览器，直接应用样式
            setTimeout(() => {
              this.applyFullscreenStyles();
            }, 100);
          }

          // 应用全屏样式（如果Promise不支持的话）
          if (!fullscreenPromise || typeof fullscreenPromise.then !== 'function') {
            this.applyFullscreenStyles();
          }

          // 确保布局正确响应全屏变化
          this.$nextTick(() => {
            // 触发窗口大小变化事件，确保模型正确渲染
            window.dispatchEvent(new Event('resize'));
          });
        } catch (error) {
          console.error('进入全屏模式失败:', error);
        }
      } else {
        // 退出全屏模式
        console.log('尝试退出全屏模式');
        try {
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
          }

          // 移除全屏相关类
          document.body.classList.remove('fullscreen-mode', 'fullscreen-active');
          document.documentElement.classList.remove('fullscreen-mode', 'fullscreen-active');

          // 恢复滚动条
          document.body.style.overflow = '';
          document.documentElement.style.overflow = '';

          // 确保布局正确响应全屏变化
          this.$nextTick(() => {
            // 触发窗口大小变化事件，确保模型正确渲染
            window.dispatchEvent(new Event('resize'));
          });
        } catch (error) {
          console.error('退出全屏模式失败:', error);
        }
      }

      // 更新全屏状态
      this.isFullscreenMode = !this.isFullscreenMode;
      console.log('全屏状态已更新为:', this.isFullscreenMode ? '全屏' : '非全屏');

      // 通知父组件全屏状态已更改
      this.$emit('toggle-fullscreen');
    },

    // 请求全屏模式
    requestFullScreen() {
      const elem = document.documentElement;

      // 检查是否已经处于全屏模式
      if (document.fullscreenElement ||
          document.webkitFullscreenElement ||
          document.mozFullScreenElement ||
          document.msFullscreenElement) {
        console.log('已经处于全屏模式，无需再次请求');
        return;
      }

      // 请求全屏
      try {
        console.log('尝试请求全屏模式');

        // 使用Promise包装全屏请求，以便更好地处理兼容性
        const requestFullscreen = () => {
          return new Promise((resolve, reject) => {
            try {
              if (elem.requestFullscreen) {
                elem.requestFullscreen().then(resolve).catch(reject);
              } else if (elem.mozRequestFullScreen) { /* Firefox */
                elem.mozRequestFullScreen();
                resolve();
              } else if (elem.webkitRequestFullscreen) { /* Chrome, Safari & Opera */
                elem.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
                resolve();
              } else if (elem.msRequestFullscreen) { /* IE/Edge */
                elem.msRequestFullscreen();
                resolve();
              } else {
                reject(new Error('不支持全屏API'));
              }
            } catch (e) {
              reject(e);
            }
          });
        };

        // 执行全屏请求
        requestFullscreen()
          .then(() => {
            console.log('全屏请求成功');

            // 设置全屏标志
            this.isFullscreenMode = true;

            // 添加全屏样式 - 始终使用fullscreen-mode类
            document.body.classList.add('fullscreen-mode');
            document.documentElement.classList.add('fullscreen-mode');
            document.body.classList.remove('small-screen-mode', 'medium-screen-mode');
            document.documentElement.classList.remove('small-screen-mode', 'medium-screen-mode');

            // 添加特殊的全屏铺满类
            document.body.classList.add('fullscreen-fill');
            document.documentElement.classList.add('fullscreen-fill');

            console.log('请求全屏模式成功');
          })
          .catch((error) => {
            console.error('全屏请求失败:', error);
            // 使用备用方法
            this.applyFallbackFullscreen();
          });
      } catch (error) {
        console.error('请求全屏模式失败:', error);
        // 使用备用方法
        this.applyFallbackFullscreen();
      }
    },

    // 备用全屏方法（当浏览器API失败时使用）
    applyFallbackFullscreen() {
      console.log('使用备用全屏方法');

      // 即使全屏请求失败，也应用全屏样式
      document.body.classList.add('fullscreen-mode');
      document.documentElement.classList.add('fullscreen-mode');
      document.body.classList.remove('small-screen-mode', 'medium-screen-mode');
      document.documentElement.classList.remove('small-screen-mode', 'medium-screen-mode');

      // 添加特殊的全屏铺满类
      document.body.classList.add('fullscreen-fill');
      document.documentElement.classList.add('fullscreen-fill');

      // 设置全屏标志
      this.isFullscreenMode = true;

      // 强制刷新布局
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 200);
    },

    // 阻止滚动事件
    preventScroll(e) {
      e.preventDefault();
      e.stopPropagation();
      return false;
    },

    // 移除滚动事件监听器
    removeScrollListeners() {
      window.removeEventListener('scroll', this.preventScroll);
      window.removeEventListener('touchmove', this.preventScroll);
      window.removeEventListener('mousewheel', this.preventScroll);
      window.removeEventListener('DOMMouseScroll', this.preventScroll);
    },

    // 重置容器样式
    resetContainerStyles() {
      console.log('重置容器样式');

      // 重置透明容器样式
      const transparentContainer = document.querySelector('.transparent-container');
      if (transparentContainer) {
        transparentContainer.style.width = '';
        transparentContainer.style.height = '';
        transparentContainer.style.position = '';
        transparentContainer.style.top = '';
        transparentContainer.style.left = '';
        transparentContainer.style.margin = '';
        transparentContainer.style.padding = '';
        transparentContainer.style.overflow = '';
      }

      // 重置dashboard容器样式
      const dashboard = this.$refs.dashboardContainer;
      if (dashboard) {
        dashboard.style.width = '';
        dashboard.style.height = '';
        dashboard.style.position = '';
        dashboard.style.top = '';
        dashboard.style.left = '';
        dashboard.style.margin = '';
        dashboard.style.padding = '';
        dashboard.style.overflow = '';
        dashboard.style.maxWidth = '';
        dashboard.style.maxHeight = '';
      }

      // 重置3D模型容器样式
      const centerColumnContainer = document.querySelector('.center-column-container');
      if (centerColumnContainer) {
        centerColumnContainer.style.width = '';
        centerColumnContainer.style.height = '';
        centerColumnContainer.style.position = '';
        centerColumnContainer.style.top = '';
        centerColumnContainer.style.left = '';
        centerColumnContainer.style.margin = '';
        centerColumnContainer.style.padding = '';
        centerColumnContainer.style.zIndex = '';
      }

      // 重置3D模型容器样式
      const modelContainer = document.querySelector('#center-model-container');
      if (modelContainer) {
        modelContainer.style.position = '';
        modelContainer.style.top = '';
        modelContainer.style.left = '';
        modelContainer.style.width = '100%';
        modelContainer.style.height = '100%';
        modelContainer.style.zIndex = '';
        modelContainer.style.right = '';
        modelContainer.style.bottom = '';
        modelContainer.style.overflow = '';
      }

      // 重置3D模型canvas样式
      const canvas = document.querySelector('#center-model-container canvas');
      if (canvas) {
        canvas.style.position = '';
        canvas.style.top = '';
        canvas.style.left = '';
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        canvas.style.zIndex = '';
        canvas.style.right = '';
        canvas.style.bottom = '';
        canvas.style.overflow = '';
      }

      // 重置左列位置
      const leftColumnWrapper = document.querySelector('.left-column-wrapper');
      if (leftColumnWrapper) {
        leftColumnWrapper.style.position = '';
        leftColumnWrapper.style.left = '';
        leftColumnWrapper.style.top = '';
        leftColumnWrapper.style.height = '';
        leftColumnWrapper.style.zIndex = '';
        leftColumnWrapper.style.margin = '';
        leftColumnWrapper.style.padding = '';
      }

      // 重置右列位置
      const rightColumnWrapper = document.querySelector('.right-column-wrapper');
      if (rightColumnWrapper) {
        rightColumnWrapper.style.position = '';
        rightColumnWrapper.style.right = '';
        rightColumnWrapper.style.top = '';
        rightColumnWrapper.style.height = '';
        rightColumnWrapper.style.zIndex = '';
        rightColumnWrapper.style.margin = '';
        rightColumnWrapper.style.padding = '';
      }

      // 重置header位置
      const headerContainer = document.querySelector('.header-container');
      if (headerContainer) {
        headerContainer.style.position = '';
        headerContainer.style.top = '';
        headerContainer.style.left = '';
        headerContainer.style.width = '';
        headerContainer.style.height = '';
        headerContainer.style.zIndex = '';
        headerContainer.style.margin = '';
        headerContainer.style.padding = '';
      }

      // 重置主内容区域
      const mainContent = document.querySelector('.main-content');
      if (mainContent) {
        mainContent.style.position = '';
        mainContent.style.top = '';
        mainContent.style.left = '';
        mainContent.style.width = '';
        mainContent.style.height = '';
        mainContent.style.zIndex = '';
        mainContent.style.margin = '';
        mainContent.style.padding = '';
      }

      // 触发窗口大小变化事件，强制重新计算布局
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 100);
    },

    // 处理ESC键退出全屏
    handleEscKey(event) {
      if (event.key === 'Escape') {
        this.exitFullscreen();
      }
    },

    // 退出全屏
    exitFullscreen() {
      console.log('尝试退出全屏模式');

      // 强制退出全屏模式，无论浏览器API是否正常工作
      const forceExitFullscreen = () => {
        console.log('执行强制退出全屏');

        // 移除ESC键监听
        document.removeEventListener('keydown', this.handleEscKey);

        // 移除所有特殊模式样式
        document.body.classList.remove('fullscreen-mode', 'small-screen-mode', 'medium-screen-mode', 'large-screen-mode', 'fullscreen-active', 'fullscreen-fill');
        document.documentElement.classList.remove('fullscreen-mode', 'small-screen-mode', 'medium-screen-mode', 'large-screen-mode', 'fullscreen-active', 'fullscreen-fill');

        // 恢复背景色
        document.body.style.backgroundColor = '';
        document.documentElement.style.backgroundColor = '';

        // 恢复滚动
        document.body.style.overflow = '';
        document.documentElement.style.overflow = '';

        // 移除所有可能添加的滚动事件监听器
        this.removeScrollListeners();

        // 恢复文档尺寸
        document.body.style.width = '';
        document.body.style.height = '';
        document.body.style.maxWidth = '';
        document.body.style.maxHeight = '';
        document.documentElement.style.width = '';
        document.documentElement.style.height = '';
        document.documentElement.style.maxWidth = '';
        document.documentElement.style.maxHeight = '';
        document.documentElement.style.position = '';

        // 更新全屏状态
        this.isFullscreenMode = false;

        // 重置所有容器样式
        this.resetContainerStyles();

        // 重新应用缩放
        setTimeout(() => {
          // 重置3D模型容器样式
          const modelContainer = document.querySelector('#center-model-container');
          if (modelContainer) {
            modelContainer.style.position = '';
            modelContainer.style.top = '';
            modelContainer.style.left = '';
            modelContainer.style.width = '100%';
            modelContainer.style.height = '100%';
            modelContainer.style.zIndex = '';
          }

          // 重置3D模型canvas样式
          const canvas = document.querySelector('#center-model-container canvas');
          if (canvas) {
            canvas.style.position = '';
            canvas.style.top = '';
            canvas.style.left = '';
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            canvas.style.zIndex = '';
          }

          // 再次触发窗口大小变化事件，确保图表正确渲染
          setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
          }, 300);
        }, 100);

        console.log('已强制退出全屏模式');
      };

      // 使用Promise包装退出全屏请求
      const exitFullscreenPromise = () => {
        return new Promise((resolve, reject) => {
          try {
            // 检查当前是否处于全屏状态
            if (document.fullscreenElement ||
                document.webkitFullscreenElement ||
                document.mozFullScreenElement ||
                document.msFullscreenElement) {

              // 根据浏览器支持选择合适的退出全屏方法
              if (document.exitFullscreen) {
                document.exitFullscreen().then(resolve).catch(reject);
              } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
                resolve();
              } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
                resolve();
              } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
                resolve();
              } else {
                reject(new Error('不支持退出全屏API'));
              }
            } else {
              // 如果不是全屏状态，直接解析
              resolve();
            }
          } catch (e) {
            reject(e);
          }
        });
      };

      try {
        // 执行退出全屏请求
        exitFullscreenPromise()
          .then(() => {
            console.log('标准退出全屏成功');
            forceExitFullscreen(); // 无论如何都执行强制退出，确保UI状态正确
          })
          .catch(error => {
            console.error('标准退出全屏失败:', error);
            forceExitFullscreen();
          });
      } catch (error) {
        console.error('退出全屏过程中发生错误:', error);
        forceExitFullscreen();
      } finally {
        // 无论如何，确保UI状态正确
        setTimeout(() => {
          // 如果2秒后仍然处于全屏状态，强制退出
          if (this.isFullscreenMode) {
            console.warn('退出全屏超时，强制退出');
            forceExitFullscreen();
          }
        }, 2000);
      }
    },

    // 处理全屏变化事件
    handleFullscreenChange() {
      // 检查全屏状态 - 兼容不同浏览器
      const isNowFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );

      // 获取当前视口尺寸
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      console.log('全屏状态变化:', isNowFullscreen ? '进入全屏' : '退出全屏');
      console.log('视口尺寸:', viewportWidth, 'x', viewportHeight);

      // 更新全屏状态
      this.isFullscreenMode = isNowFullscreen;

      // 确保全屏状态正确
      if (isNowFullscreen) {
        console.log('应用全屏模式样式');

        // 添加全屏相关类
        document.body.classList.add('fullscreen-mode', 'fullscreen-active');
        document.documentElement.classList.add('fullscreen-mode', 'fullscreen-active');

        // 隐藏滚动条
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';

        // 确保地图组件正确响应全屏变化
        this.$nextTick(() => {
          // 触发窗口大小变化事件，确保地图正确渲染
          window.dispatchEvent(new Event('resize'));
        });
      } else {
        console.log('应用非全屏模式样式');

        // 移除全屏相关类
        document.body.classList.remove('fullscreen-mode', 'fullscreen-active');
        document.documentElement.classList.remove('fullscreen-mode', 'fullscreen-active');

        // 恢复滚动条
        document.body.style.overflow = '';
        document.documentElement.style.overflow = '';

        // 移除所有可能添加的滚动事件监听器
        this.removeScrollListeners();

        // 恢复文档尺寸
        document.body.style.width = '';
        document.body.style.height = '';
        document.body.style.maxWidth = '';
        document.body.style.maxHeight = '';
        document.documentElement.style.width = '';
        document.documentElement.style.height = '';
        document.documentElement.style.maxWidth = '';
        document.documentElement.style.maxHeight = '';
        document.documentElement.style.position = '';

        // 重置所有容器样式
        this.resetContainerStyles();

        // 延迟重新应用缩放，确保DOM已经更新
        setTimeout(() => {
          // 退出全屏时重新应用缩放
          // this.applyScale();

          // 应用header样式
          this.applyHeaderStyles();

          // 再次触发窗口大小变化事件，确保图表正确渲染
          // 使用单一的延迟触发，避免多次触发导致的闪烁
          setTimeout(() => {
            console.log('退出全屏 - 触发单一resize事件');
            // 先应用样式
            this.applyHeaderStyles();

            // 然后触发单一resize事件
            window.dispatchEvent(new Event('resize'));
          }, 500);
        }, 100);
      }
    },

    // 处理窗口大小变化
    handleResize() {
      // 确保在窗口大小变化时重新计算布局
      this.$nextTick(() => {
        // 可以在这里添加任何需要在窗口大小变化时执行的逻辑
      });
    },

    // 应用全屏样式
    applyFullscreenStyles() {
      // 添加全屏相关类
      document.body.classList.add('fullscreen-mode', 'fullscreen-active');
      document.documentElement.classList.add('fullscreen-mode', 'fullscreen-active');

      // 隐藏滚动条
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';
    }
  }
}
</script>

<style scoped>

.dashboard-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: transparent; /* 使用透明背景，直接显示大屏背景 */
  backdrop-filter: none; /* 移除模糊效果 */
  -webkit-backdrop-filter: none; /* 移除模糊效果 */
  border-radius: 0; /* 移除圆角，使其完全铺满 */
  box-shadow: none; /* 移除阴影 */
  position: absolute;
  z-index: 9999;
  overflow: visible;
  transform-origin: top left;
  box-sizing: border-box;
  max-width: none;
  transition: transform 0.3s ease-out;
  margin: 0;
  left: 0;
  top: 0;
}

.header-container {
  position: absolute;
  width: 100%;
  height: 50px !important; /* 调整高度 */
  z-index: 200;
  /* 确保在3D模型上方但不覆盖左右列 */
  pointer-events: auto;
  /* 确保可以接收鼠标事件 */
  padding: 0 !important;
  /* 移除内边距 */
  margin: 0 !important;
  /* 移除所有间距 */
  left: 0;
  top: 0;
  right: 0;
  box-sizing: border-box;
  overflow: visible;
  background-color: transparent;
}

.main-content {
  display: flex;
  justify-content: space-between;
  height: calc(100% - 50px) !important; /* 调整高度，为header留出空间 */
  overflow: visible;
  position: absolute;
  top: 50px !important; /* 从header下方开始，无间距 */
  left: 0;
  padding: 0 !important;
  width: 100%;
  margin: 0 !important;
  z-index: 300;
  pointer-events: none !important;
  touch-action: auto !important;
  box-sizing: border-box;
}

/* 全屏模式下的主内容区域 */
.fullscreen-fill .main-content {
  width: 100vw !important;
  justify-content: space-between !important;
  align-items: stretch !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  overflow: visible !important;
  display: flex !important;
  position: fixed !important;
  top: 40px !important;
  left: 0 !important;
  height: calc(100vh - 40px) !important;
  z-index: 300 !important;
  pointer-events: none !important;
  visibility: visible !important;
}

/* 全屏模式下的左列 */
.fullscreen-fill .left-column-wrapper {
  position: fixed !important;
  left: 0 !important;
  top: 40px !important;
  height: calc(100vh - 40px) !important;
  z-index: 300 !important;
  margin: 0 !important;
  padding: 0 !important;
  pointer-events: auto !important;
  visibility: visible !important;
}

/* 全屏模式下的右列 */
.fullscreen-fill .right-column-wrapper {
  position: fixed !important;
  right: 0 !important;
  top: 40px !important;
  height: calc(100vh - 40px) !important;
  z-index: 300 !important;
  margin: 0 !important;
  padding: 0 !important;
  pointer-events: auto !important;
  visibility: visible !important;
}

/* 全屏模式下的头部 */
.fullscreen-fill .header-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 40px !important;
  z-index: 500 !important;
  margin: 0 !important;
  padding: 0 !important;
  visibility: visible !important;
  display: block !important;
}

/* 全屏模式下的中央列 */
.fullscreen-fill .center-column-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 50 !important;
  margin: 0 !important;
  padding: 0 !important;
  background-color: transparent !important;
  backdrop-filter: none !important;
  overflow: visible !important;
  pointer-events: auto !important;
  touch-action: auto !important;
  visibility: visible !important;
}

/* 确保全屏模式下3D模型容器正确显示 */
.fullscreen-fill #center-model-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 50 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: visible !important;
  pointer-events: auto !important;
  touch-action: auto !important;
}

/* 确保全屏模式下3D模型canvas正确显示 */
.fullscreen-fill #center-model-container canvas {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 50 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: visible !important;
  pointer-events: auto !important;
  touch-action: auto !important;
}

/* 全屏模式下的透明容器 */
.fullscreen-fill .transparent-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 100 !important;
  margin: 0 !important;
  padding: 0 !important;
  background-color: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  overflow: hidden !important;
}

/* 底部版权信息样式 */
.footer-info {
  position: absolute;
  bottom: 5px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  z-index: 100;
  text-align: center;
  gap: 20px;
}

.copyright, .version {
  opacity: 0.7;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
}

/* 添加响应式调整 */
@media screen and (max-width: 1600px) {
  .main-content {
    padding: 0;
    /* 移除内边距 */
    justify-content: space-between;
  }
}

@media screen and (max-width: 1366px) {
  .main-content {
    padding: 0;
    /* 移除内边距 */
    justify-content: space-between;
  }
}

/* 统一屏幕模式样式 */
:global(.screen-mode) {
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  background-color: transparent !important;
  width: 100vw !important;
  height: 100vh !important;
}

/* 全屏铺满模式样式 */
:global(.fullscreen-fill) {
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  background-color: #000 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

/* 全屏模式下的模块容器 */
:global(.fullscreen-fill .module-container) {
  width: 100% !important;
  height: auto !important;
  margin-bottom: 10px !important;
  max-height: none !important;
  overflow: visible !important;
}

/* 全屏模式下的面板 */
:global(.fullscreen-fill .panel) {
  width: 100% !important;
  height: auto !important;
  margin-bottom: 10px !important;
  max-height: none !important;
  overflow: visible !important;
}

/* 全屏模式下的图表容器 */
:global(.fullscreen-fill .chart-container) {
  width: 100% !important;
  height: auto !important;
  min-height: 200px !important;
  max-height: none !important;
  overflow: visible !important;
}

/* 全屏模式下的所有容器 */
:global(.fullscreen-fill .container),
:global(.fullscreen-fill .model-container),
:global(.fullscreen-fill .weather-details),
:global(.fullscreen-fill .system-info),
:global(.fullscreen-fill .module-item),
:global(.fullscreen-fill .chart-wrapper) {
  width: 100% !important;
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

/* 确保全屏模式下右侧列的所有内容都能完整显示 */
:global(.fullscreen-fill .right-column .module-container),
:global(.fullscreen-fill .right-column .panel),
:global(.fullscreen-fill .right-column .chart-container),
:global(.fullscreen-fill .right-column .container),
:global(.fullscreen-fill .right-column .module-item),
:global(.fullscreen-fill .right-column .chart-wrapper),
:global(.fullscreen-fill .right-column div) {
  width: 100% !important;
  max-width: 380px !important;
  overflow: visible !important;
  margin-bottom: 10px !important;
  box-sizing: border-box !important;
}

/* 确保全屏模式下左侧列的所有内容都能完整显示 */
:global(.fullscreen-fill .left-column .module-container),
:global(.fullscreen-fill .left-column .panel),
:global(.fullscreen-fill .left-column .chart-container),
:global(.fullscreen-fill .left-column .container),
:global(.fullscreen-fill .left-column .module-item),
:global(.fullscreen-fill .left-column .chart-wrapper),
:global(.fullscreen-fill .left-column div) {
  width: 100% !important;
  max-width: 300px !important;
  overflow: visible !important;
  margin-bottom: 10px !important;
  box-sizing: border-box !important;
}

/* 确保屏幕模式下背景色透明 */
body.screen-mode,
html.screen-mode {
  background-color: transparent !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
}

/* 屏幕模式下的仪表盘容器 */
.screen-mode .dashboard-container {
  transform-origin: top left !important;
  margin: 0 !important;
  padding: 0 !important;
  position: absolute !important;
  z-index: 9999 !important;
  overflow: visible !important;
  width: 1920px !important;
  height: 1080px !important;
  border-radius: 0 !important; /* 移除圆角 */
  box-shadow: none !important; /* 移除阴影 */
  left: 0 !important;
  top: 0 !important;
  background-color: transparent !important;
}

/* 全屏模式下的仪表盘容器 */
.fullscreen-fill .dashboard-container {
  transform-origin: top left !important;
  margin: 0 !important;
  padding: 0 !important;
  position: absolute !important;
  z-index: 9999 !important;
  overflow: visible !important;
  width: 1920px !important;
  height: 1080px !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  left: 0 !important;
  top: 0 !important;
  background-color: transparent !important;
}

/* 确保所有屏幕模式下高科技背景正确显示 */
.tech-background {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 0 !important;
  background-color: transparent !important;
}

/* 确保所有列正确显示 */
.left-column {
  height: 100% !important;
  overflow: visible !important;
  z-index: 100 !important;
  position: relative !important;
  pointer-events: auto !important;
}

.right-column-wrapper {
  position: absolute !important;
  right: 0 !important;
  top: 40px !important;
  height: calc(100% - 40px) !important;
  z-index: 300 !important;
  display: flex !important;
  margin: 0 !important;
  padding: 0 !important;
}

.right-column {
  height: 100% !important;
  overflow: visible !important;
  z-index: 100 !important;
  position: relative !important;
  pointer-events: auto !important;
}

/* 确保右侧列有足够的宽度显示内容 - 使用固定宽度 */
.right-column {
  min-width: 380px !important;
  max-width: 380px !important;
  width: 380px !important;
  margin-left: 5px !important;
  overflow: visible !important;
}

/* 确保左侧列与右侧列宽度一致 - 使用相同的固定宽度 */
.left-column {
  min-width: 380px !important;
  max-width: 380px !important;
  width: 380px !important;
  margin-right: 5px !important;
  overflow: visible !important;
}

/* 全屏模式下的特殊样式 */
.fullscreen-fill .right-column-wrapper {
  position: absolute !important;
  right: 0 !important;
  top: 40px !important;
  height: calc(100% - 40px) !important;
  z-index: 300 !important;
  display: flex !important;
  margin: 0 !important;
  padding: 0 !important;
}

.fullscreen-fill .right-column {
  min-width: 380px !important;
  max-width: 380px !important;
  width: 380px !important;
  margin-left: 0 !important; /* 移除左边距，因为已经通过绝对定位放置 */
  flex: 0 0 auto !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
  align-items: stretch !important;
  height: 100% !important;
  overflow: visible !important;
}

.fullscreen-fill .left-column {
  min-width: 380px !important;
  max-width: 380px !important;
  width: 380px !important;
  margin-right: 10px !important;
  flex: 0 0 auto !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
  align-items: stretch !important;
  height: 100% !important;
  overflow: visible !important;
}

/* 全屏模式下的中间列 */
.fullscreen-fill .center-column {
  flex: 1 1 auto !important;
  min-width: 400px !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  max-width: calc(100% - 860px) !important; /* 100% - (左右列宽度 + 边距)，调整为更准确的值，左右各380px + 边距 */
}

/* 3D模型容器 */
.center-column {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 1 !important;
  background-color: transparent !important;
}

.exit-fullscreen-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  background-color: rgba(0, 168, 255, 0.7);
  color: white;
  border: 2px solid white;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(0, 168, 255, 0.8);
  opacity: 0.9;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 168, 255, 0.7);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(0, 168, 255, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(0, 168, 255, 0);
  }
}

.exit-fullscreen-btn:hover {
  background-color: rgba(0, 168, 255, 1);
  opacity: 1;
  transform: scale(1.1);
}

/* 确保退出按钮在小屏幕上也能正确显示 */
@media screen and (max-width: 1366px) {
  .exit-fullscreen-btn {
    top: 10px;
    right: 10px;
    width: 45px;
    height: 45px;
    font-size: 18px;
    border-width: 2px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* 全屏模式下的全局样式已在上方定义 */

/* 添加全屏活动状态样式 */
:global(.fullscreen-active) {
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  background: linear-gradient(135deg, rgba(4, 16, 34, 0.95) 0%, rgba(8, 32, 64, 0.95) 50%, rgba(5, 24, 53, 0.95) 100%) !important;
  backdrop-filter: blur(5px) !important;
  width: 100vw !important;
  height: 100vh !important;
}

/* 确保全屏模式下内容完全显示 */
.fullscreen-active .dashboard-container {
  height: 100vh !important;
  overflow: visible !important;
  position: absolute !important;
  z-index: 9999 !important;
  left: 0 !important;
  top: 0 !important;
}

/* 确保header容器始终可见 */
.header-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  pointer-events: auto;
  margin-bottom: 0;
  height: 50px !important;
  overflow: visible;
  background-color: transparent;
}

/* 确保dashboard-wrapper正确定位所有元素 */
.dashboard-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: visible;
}

/* 确保退出全屏后图表容器可见 */
.chart-container,
.environment-chart-container,
.attitude-chart-container {
  overflow: visible !important;
  visibility: visible !important;
  display: flex !important;
  position: relative;
  z-index: 12;
}

/* 修复遮盖问题的样式调整 */
.transparent-container {
  background-color: transparent !important;
  backdrop-filter: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  z-index: 5 !important; /* 降低z-index，避免遮挡其他元素 */
  overflow: visible !important;
  position: relative !important;
}

/* 确保主内容区域不被遮盖 */
.main-content {
  position: relative !important;
  z-index: 300 !important;
  overflow: visible !important;
  height: calc(100% - 40px) !important;
  width: 100% !important;
  display: flex !important;
  justify-content: space-between !important;
  pointer-events: none !important;
  visibility: visible !important;
}

/* 确保左右列正确显示 */
.left-column-wrapper, .right-column-wrapper {
  z-index: 500 !important; /* 确保在其他元素之上 */
  pointer-events: auto !important;
  overflow: visible !important;
  visibility: visible !important;
  display: block !important;
}

/* 确保头部不被遮盖 */
.header-container {
  position: relative !important;
  z-index: 500 !important;
  width: 100% !important;
  height: 50px !important;
  overflow: visible !important;
  visibility: visible !important;
  display: block !important;
}

/* 确保所有模块容器可见 */
:deep(.module-container),
:deep(.panel),
:deep(.chart-container) {
  overflow: visible !important;
  z-index: auto !important;
  position: relative !important;
}

/* 全屏模式下的特殊处理 */
:global(.fullscreen-mode) .dashboard-container,
:global(.fullscreen-fill) .dashboard-container {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 5 !important;
  overflow: visible !important;
}

/* 全屏模式下的header处理 */
:global(.fullscreen-mode) .header-container,
:global(.fullscreen-fill) .header-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 80px !important;
  z-index: 500 !important;
  visibility: visible !important;
  display: block !important;
  overflow: visible !important;
}

/* 确保图表内容可见 */
:deep(.echarts),
:deep(.chart-wrapper) {
  z-index: 15 !important;
  position: relative !important;
  overflow: visible !important;
}
</style>
<style>
/* 全局样式修复 */
.screen-container {
  background-color: transparent !important;
  overflow: visible !important;
}

.detail-view-container {
  background-color: transparent !important;
  z-index: 100 !important;
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  overflow: visible !important;
}

/* 确保所有图表和模块正确显示 */
.module-container,
.panel,
.chart-container,
.model-container,
.chart-wrapper {
  overflow: visible !important;
  z-index: auto !important;
  position: relative !important;
}

/* 确保左右列正确显示 */
.left-column,
.right-column {
  z-index: 500 !important;
  position: relative !important;
  overflow: visible !important;
  pointer-events: auto !important;
  visibility: visible !important;
  display: block !important;
}

/* 确保中央列正确显示 */
.center-column {
  z-index: 50 !important;
  position: relative !important;
  overflow: visible !important;
  background-color: transparent !important;
}

/* 移除顶部边距和header边距 */
.header-container {
  margin: 0 !important;
  padding: 0 !important;
  height: 50px !important; /* 调整header高度 */
}

/* 调整header内部元素 */
.header {
  margin: 0 !important;
  padding: 0 !important;
  height: 50px !important;
  min-height: 50px !important;
  max-height: 50px !important;
  visibility: visible !important;
  display: block !important;
  z-index: 500 !important;
}

/* 调整主内容区域，减少与header的间距 */
.main-content {
  margin: 0 !important;
  padding: 0 !important;
  height: calc(100% - 80px) !important; /* 调整为header新高度 */
  top: 80px !important; /* 调整为header新高度 */
}

/* 移除模块间的边距 */
.widget {
  margin: 0 0 5px 0 !important; /* 只保留5px的底部间距 */
  padding: 0 !important;
}

/* 移除模块内容的边距 */
.widget-content {
  margin: 0 !important;
  padding: 5px !important; /* 只保留5px的内边距 */
}

/* 移除模块头部的边距 */
.widget-header {
  margin: 0 !important;
  padding: 2px 5px !important; /* 减小头部内边距 */
  height: 24px !important; /* 减小头部高度 */
  line-height: 24px !important; /* 调整行高与高度一致 */
}

/* 调整左右列与顶部的间距 */
.left-column-wrapper,
.right-column-wrapper {
  margin: 0 !important;
  padding: 0 !important;
  top: 80px !important; /* 调整为header新高度 */
  height: calc(100% - 80px) !important; /* 调整高度 */
  z-index: 500 !important; /* 确保在模型上方 */
}

/* 调整左右列内部边距 */
.left-column,
.right-column {
  margin: 0 !important;
  padding: 0 !important;
}

/* 调整图表容器边距 */
.chart-container,
.module-container,
.panel {
  margin: 0 !important;
  padding: 0 !important;
}

/* 调整dashboard容器边距 */
.dashboard-container {
  margin: 0 !important;
  padding: 0 !important;
}

/* 确保全屏模式下也无边距 */
:global(.fullscreen-mode) .dashboard-container,
:global(.fullscreen-fill) .dashboard-container {
  margin: 0 !important;
  padding: 0 !important;
}

/* 调整图表内部边距 */
:deep(.echarts),
:deep(.chart-wrapper) {
  margin: 0 !important;
  padding: 0 !important;
}

/* 确保所有容器无边距 */
.transparent-container {
  margin: 0 !important;
  padding: 0 !important;
}

/* 确保3D模型容器在全屏模式下正确显示 */
#center-model-container {
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
  overflow: visible !important;
  z-index: 50 !important;
}

/* 确保全屏模式下3D模型容器正确显示 */
.fullscreen-mode #center-model-container,
.fullscreen-fill #center-model-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 50 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: visible !important;
}

/* 确保全屏模式下3D模型canvas正确显示 */
.fullscreen-mode #center-model-container canvas,
.fullscreen-fill #center-model-container canvas {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 50 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: visible !important;
}

/* 添加全局样式，在全屏模式下设置背景 */
:global(.fullscreen-mode),
:global(.fullscreen-fill) {
  background: linear-gradient(135deg, #041022 0%, #082045 50%, #041835 100%) !important;
  backdrop-filter: blur(5px) !important;
}

:global(.fullscreen-mode) body,
:global(.fullscreen-fill) body {
  background: linear-gradient(135deg, #041022 0%, #082045 50%, #041835 100%) !important;
  backdrop-filter: blur(5px) !important;
}

:global(.fullscreen-mode) .screen-container,
:global(.fullscreen-fill) .screen-container {
  background: linear-gradient(135deg, #041022 0%, #082045 50%, #041835 100%) !important;
  backdrop-filter: blur(5px) !important;
  height: 100vh !important;
  padding: 0 !important;
}

/* 确保全屏元素背景正确 */
:fullscreen,
:-webkit-full-screen,
:-moz-full-screen,
:-ms-fullscreen {
  background: linear-gradient(135deg, #041022 0%, #082045 50%, #041835 100%) !important;
  backdrop-filter: blur(5px) !important;
}

/* 确保全屏元素内的容器背景正确 */
:fullscreen .screen-container,
:-webkit-full-screen .screen-container,
:-moz-full-screen .screen-container,
:-ms-fullscreen .screen-container {
  background: linear-gradient(135deg, #041022 0%, #082045 50%, #041835 100%) !important;
  backdrop-filter: blur(5px) !important;
}
</style>
