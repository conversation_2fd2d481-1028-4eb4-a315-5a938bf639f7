<template>
  <div class="screen-container" :class="{ 'fullscreen-mode': isFullscreen }" ref="screenContainer">
    <!-- 模型背景 -->
    <div class="model-background">
      <div class="city-background"></div>
    </div>

    <!-- 星光背景效果 - 使用Canvas实现 -->
    <canvas ref="starsCanvas" class="stars-canvas"></canvas>

    <!-- 项目选择区域 - 当未选择项目时显示 -->
    <div v-if="!selectedProject" class="project-selection">
      <!-- 视图切换按钮 -->
      <div class="view-toggle">
        <el-button :type="viewMode === 'map' ? 'primary' : ''" icon="el-icon-map-location" circle @click="viewMode = 'map'"></el-button>
        <el-button :type="viewMode === 'list' ? 'primary' : ''" icon="el-icon-menu" circle @click="viewMode = 'list'"></el-button>
        <el-button :type="isFullscreen ? 'primary' : ''" icon="el-icon-full-screen" circle @click="toggleFullScreen"></el-button>
      </div>

      <!-- 项目列表视图 -->
      <div class="project-list-container" v-if="viewMode === 'list'">
        <h2 class="project-list-title">项目列表</h2>
        <div class="project-list">
          <div
            v-for="project in projectList"
            :key="project.id"
            class="project-card"
            @mouseover="showProjectTooltip(project, $event)"
            @mouseleave="hideProjectTooltip()"
            @click="handleProjectClick(project)"
          >
            <div class="project-image">
              <img :src="getProjectImage(project)" :alt="project.name">
              <div class="project-status" :class="project.status">
                {{ getProjectStatusText(project.status) }}
              </div>
            </div>
            <div class="project-info">
              <h3>{{ project.name }}</h3>
              <p class="project-account">
                <i class="el-icon-office-building"></i> {{ project.account_name || '暂无单位' }}
              </p>
              <p class="project-location">
                <i class="el-icon-location"></i> {{ project.location }}
              </p>
              <p class="project-progress">
                <span>进度: {{ project.progress }}%</span>
                <el-progress
                  :percentage="project.progress"
                  :color="getProjectStatusColor(project.status)"
                ></el-progress>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目地图视图 -->
      <div class="map-container" v-if="viewMode === 'map'">
        <el-card class="map-card">
          <Map3D
            ref="map3d"
            :project-id="projectId"
            :project-list="projectList"
            :device-list="deviceList"
            :gateway-list="gatewayList"
            :camera-list="cameraList"
            @device-click="handleDeviceClick"
            @gateway-click="handleGatewayClick"
            @camera-click="handleCameraClick"
            @project-click="handleProjectClick"
            @webgl-error="handleWebGLError"
          />
        </el-card>
      </div>

      <!-- 项目详情悬浮提示 -->
      <div class="project-tooltip" v-if="hoveredProject" :style="popupStyle">
        <h3>{{ hoveredProject.name }}</h3>
        <p><strong>项目编号:</strong> {{ hoveredProject.projectNo }}</p>
        <p><strong>所属单位:</strong> {{ hoveredProject.account_name || '暂无单位' }}</p>
        <p><strong>位置:</strong> {{ hoveredProject.location }}</p>
        <p><strong>描述:</strong> {{ hoveredProject.description }}</p>
        <p><strong>安装日期:</strong> {{ formatDate(hoveredProject.installation_date || hoveredProject.installationDate || hoveredProject.installDate) }}</p>
        <p><strong>状态:</strong> {{ getProjectStatusText(hoveredProject.status) }}</p>
        <p><strong>进度:</strong> {{ hoveredProject.progress }}%</p>
        <p><strong>预计完成时间:</strong> {{ formatRelativeDate(hoveredProject.estimated_completion || hoveredProject.estimatedCompletion) }}</p>
        <p v-if="hoveredProject.bridgeMachineModels"><strong>桥梁机械型号:</strong> {{ hoveredProject.bridgeMachineModels.join(', ') }}</p>
        <div class="tooltip-footer">点击查看详情</div>
      </div>
    </div>

    <!-- 项目详情大屏组件 -->
    <div v-else class="detail-view-container">
      <!-- 新版详情视图 -->
      <new-project-detail-view
        class="project-detail-view"
        :projectId="selectedProject ? selectedProject.id : ''"
        :selectedProject="selectedProject"
        :currentTime="currentTime"
        :isFullscreen="isFullscreen"
        :deviceList="deviceList"
        :modelView="modelView"
        @back-to-list="backToProjectList"
        @toggle-fullscreen="toggleFullScreen"
      />
    </div>

    <!-- 设备详情对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <div v-if="currentDevice">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="设备名称">{{ currentDevice.device_name }}</el-descriptions-item>
          <el-descriptions-item label="设备号">{{ currentDevice.device_code }}</el-descriptions-item>
          <el-descriptions-item label="设备状态">
            <el-tag :type="getStatusType(currentDevice.status)">
              {{ getStatusText(currentDevice.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(currentDevice.created_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import NewProjectDetailView from './components/NewProjectDetailView.vue';
import Map3D from '@/components/Map3D'
import { getProjectDetail, getAccountProjects } from '@/api/project'
import { getDeviceList } from '@/api/device'
import { getGatewayList } from '@/api/gateway'
import { getCameraList } from '@/api/camera'
import { getUserInfo } from '@/api/admin'

export default {
  name: 'ScreenManagement',
  components: {
    NewProjectDetailView,
    Map3D
  },
  data() {
    return {
      projectId: '',
      projectList: [],
      selectedProject: null,
      selectedPopupProject: null,
      deviceList: [],
      currentTime: '',
      timer: null,
      isFullscreen: false,
      modelView: '3d',
      animationFrame: null,
      starsInitialized: false,
      hoveredProject: null,
      popupStyle: {},
      connectionLines: [],
      viewMode: 'map', // 默认设置为'map'视图而非'list'
      gatewayList: [],
      cameraList: [],
      dialogVisible: false,
      dialogTitle: '设备详情',
      currentDevice: null,
      cityCoordinates: {
        '北京': { x: 50, y: 30 },
        '上海': { x: 60, y: 40 },
        '广州': { x: 70, y: 50 },
        '深圳': { x: 80, y: 60 },
        '杭州': { x: 90, y: 70 }
      }
    }
  },
  computed: {
    deviceCount() {
      return this.deviceList.length
    },
    onlineDeviceCount() {
      return this.deviceList.filter(device =>
        device.status === 'online' || device.status === 'normal').length
    },
    warningDeviceCount() {
      return this.deviceList.filter(device => device.status === 'warning').length
    },
    offlineDeviceCount() {
      return this.deviceList.filter(device => device.status === 'offline').length
    },
    dateStr() {
      return this.currentTime.split(' ')[0]
    },
    timeStr() {
      const time = this.currentTime.split(' ')[1]
      return time ? time : ''
    }
  },
  watch: {
    projectList: {
      deep: true,
      handler(newVal, oldVal) {
        // 项目列表变化时的处理逻辑
        console.log('项目列表已更新，当前数量:', newVal ? newVal.length : 0);

        // 更新项目卡片动画延迟
        this.$nextTick(() => {
          const projectCards = document.querySelectorAll('.project-card');
          projectCards.forEach((card, index) => {
            card.style.setProperty('--animation-order', index % 5);
          });
        });

        // 检查map3d组件是否存在且有updateProjects方法
        if (this.$refs.map3d) {
          try {
            // 检查map3d组件是否有updateProjects方法
            if (typeof this.$refs.map3d.updateProjects === 'function') {
              this.$refs.map3d.updateProjects(newVal);
            } else {
              // 如果没有updateProjects方法，可以尝试其他可能的更新方式
              // 例如设置项目列表prop
              console.log('Map3D组件没有updateProjects方法，尝试通过props更新');
            }
          } catch (error) {
            console.error('更新Map3D项目数据时出错:', error);
          }
        }
      }
    }
  },
  created() {
    // Initialize current time
    this.updateCurrentTime()
    // Start time update timer
    this.timer = setInterval(this.updateCurrentTime, 1000)
  },
  mounted() {
    this.init()
    console.log('screen/index.vue组件已挂载，默认视图模式:', this.viewMode)

    // 初始化星光背景
    this.initStarsBackground()

    // 设置项目卡片的动画延迟
    this.$nextTick(() => {
      const projectCards = document.querySelectorAll('.project-card');
      projectCards.forEach((card, index) => {
        card.style.setProperty('--animation-order', index % 5);
      });
    });

    // 监听窗口大小改变，更新弹窗位置和星光背景
    window.addEventListener('resize', () => {
      this.updatePopupPosition()
      this.initStarsBackground()
    })

    // 监听全屏变化
    document.addEventListener('fullscreenchange', () => {
      this.isFullscreen = !!document.fullscreenElement;
      this.initStarsBackground() // 全屏变化时重新初始化星光背景
    });
    document.addEventListener('webkitfullscreenchange', () => {
      this.isFullscreen = !!document.webkitFullscreenElement;
      this.initStarsBackground()
    });
    document.addEventListener('mozfullscreenchange', () => {
      this.isFullscreen = !!document.mozFullscreenElement;
      this.initStarsBackground()
    });
    document.addEventListener('MSFullscreenChange', () => {
      this.isFullscreen = !!document.msFullscreenElement;
      this.initStarsBackground()
    });

    // 检查URL中是否有项目ID参数
    const projectId = this.$route.query.projectId;
    if (projectId) {
      console.log('URL中包含项目ID参数:', projectId);
    }

    // 检查URL中是否有全屏参数
    const fullscreenParam = this.$route.query.fullscreen;
    if (fullscreenParam === 'true' && !document.fullscreenElement) {
      console.log('URL中包含全屏参数，尝试进入全屏模式');
      // 设置全屏状态标志
      this.isFullscreen = true;
      // 延迟一点时间再进入全屏模式，确保组件已完全挂载
      setTimeout(() => {
        if (!document.fullscreenElement) {
          console.log('执行全屏切换');
          this.toggleFullScreen();
        }
      }, 800);
    }
  },
  beforeDestroy() {
    // 清除动画和计时器
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
    }
    if (this.timer) {
      clearInterval(this.timer)
    }

    // 移除窗口大小改变监听
    window.removeEventListener('resize', this.updatePopupPosition)
    window.removeEventListener('resize', this.initStarsBackground)

    // 移除全屏监听
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange)

    // 清理Three.js场景
    this.disposeThreeScene()

    // 重置全局状态，避免影响其他页面
    document.body.style.overflow = ''
    document.body.style.background = ''
    document.documentElement.style.overflow = ''

    // 移除全局样式应用
    document.body.classList.remove('screen-mode')
    document.documentElement.classList.remove('screen-mode')

    // 移除任何可能添加的全局样式
    const styleElements = document.head.querySelectorAll('style[data-screen-style]')
    styleElements.forEach(element => {
      document.head.removeChild(element)
    })

    // 清除任何引用
    this.selectedProject = null
    this.deviceList = []
    this.projectList = []
    this.hoveredProject = null

    // 强制重新渲染文档
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'))
    }, 100)
  },
  methods: {
    // 初始化方法
    init() {
      // 更新当前时间
      this.updateCurrentTime();

      // 调试输出
      console.log('【调试】初始化screen/index.vue组件');

      // 设置定时器，每秒更新一次时间
      this.timer = setInterval(this.updateCurrentTime, 1000);

      // 根据URL参数设置初始视图模式，如果没有URL参数则保持默认的'map'
      const viewParam = this.$route.query.view;
      if (viewParam) {
        this.viewMode = viewParam;
      }

      // 加载项目数据
      this.loadProjectData(() => {
        // 项目数据加载完成后，检查URL中是否有项目ID
        const projectId = this.$route.query.projectId;
        if (projectId && this.projectList.length > 0) {
          console.log('URL中包含项目ID参数，尝试加载项目:', projectId);
          // 查找项目并加载
          const projectIdNum = parseInt(projectId);
          const project = this.projectList.find(p => p.id === projectIdNum);
          if (project) {
            console.log('根据URL参数找到项目:', project.name);
            this.handleProjectClick(project);
          } else {
            console.error('找不到ID为', projectId, '的项目');
          }
        }
      });

      // 启动数据轮询
      this.startDataPolling();

      // 输出调试信息
      console.log('【调试】项目数据实际类型检查：');
      setTimeout(() => {
        if (this.projectList && this.projectList.length > 0) {
          const firstProject = this.projectList[0];
          console.log('【调试】第一个项目数据ID类型:', typeof firstProject.id);
          console.log('【调试】第一个项目数据详情:', JSON.stringify(firstProject));
        }
      }, 1000);

      // 添加测试函数到window对象，便于在控制台调试
      window.testProjectClick = (projectId) => {
        console.log('手动测试点击项目:', projectId);
        const project = this.projectList.find(p => p.id === projectId);
        if (project) {
          console.log('找到项目:', project.name);
          this.handleProjectClick(project);
        } else {
          console.error('找不到ID为', projectId, '的项目');
        }
      };
    },

    // 加载项目数据
    async loadProjectData(callback) {
      try {
        // 先获取用户信息
        const userResponse = await getUserInfo();
        console.log('获取用户信息:', userResponse);
        if (!userResponse || userResponse.code !== 1) {
          console.error('获取用户信息失败:', userResponse?.msg || '未知错误');
          this.$message.error('获取用户信息失败，请稍后重试');
          return;
        }

        const userInfo = userResponse.data?.user;
        if (!userInfo) {
          console.error('用户信息为空');
          this.$message.error('用户信息获取失败，请重新登录');
          return;
        }

        // 根据用户角色获取账户ID
        let accountId;
        if (userInfo.admin === 1) {
          // 如果是管理员，使用 merid 或默认值 0
          accountId =  0;
        } else {
          // 普通用户使用 moreId
          accountId = userInfo.moreId;
        }

        if (!accountId && accountId !== 0) {
          console.error('无法获取账户ID，当前用户信息:', userInfo);
          this.$message.error('无法获取账户信息，请重新登录');
          return;
        }

        console.log('正在获取账户项目列表，账户ID:', accountId);

        // 调用API获取项目列表
        const response = await getAccountProjects(accountId);
        if (response && response.code === 0) {
          this.projectList = response.data.list || [];
          console.log('已加载项目数据:', this.projectList.length, '个项目');

          // 生成连接线
          // this.generateConnectionLines();

          // 如果URL中有项目ID参数，则直接显示该项目
          const projectId = this.$route.query.projectId;
          if (projectId && this.projectList.length > 0) {
            console.log('URL中包含项目ID参数，尝试加载项目:', projectId);
            const projectIdNum = parseInt(projectId);
            const project = this.projectList.find(p => p.id === projectIdNum);
            if (project) {
              console.log('根据URL参数找到项目:', project.name);
              this.handleProjectClick(project);
            } else {
              console.error('找不到ID为', projectId, '的项目');
            }
          }

          // 回调函数
          if (typeof callback === 'function') {
            callback();
          }
        } else {
          console.error('获取项目列表失败:', response?.message || '未知错误');
          this.$message.error('获取项目列表失败，请稍后重试');
        }
      } catch (error) {
        console.error('加载项目数据时发生错误:', error);
        this.$message.error('加载项目数据失败，请稍后重试');
      }
    },

    // 处理项目选择
    handleProjectSelect(projectId) {
      const project = this.projectList.find(p => p.id === projectId);
      if (project) {
        this.selectedProject = project;

        // 加载设备数据
        this.loadDeviceData(projectId);
      }
    },

    // 加载设备数据
    loadDeviceData(projectId) {
      console.log('加载固定设备数据（禁用模拟更新）');

      // 模拟从API获取设备数据
      setTimeout(() => {
        // 使用固定的设备状态数据
        this.deviceList = [
          {
            id: 1,
            name: '造桥机A区域监控系统',
            status: 'normal',
            gateways: [
              {
                id: 101,
                name: '网关1号',
                status: 'normal',
                sensors: [
                  { id: 1001, name: '温度传感器', value: '36.5', unit: '°C', status: 'normal' },
                  { id: 1002, name: '湿度传感器', value: '65', unit: '%', status: 'normal' },
                  { id: 1003, name: '压力传感器', value: '1.02', unit: 'MPa', status: 'normal' },
                  { id: 1004, name: '振动传感器', value: '0.15', unit: 'g', status: 'normal' }
                ]
              },
              {
                id: 102,
                name: '网关2号',
                status: 'warning',
                sensors: [
                  { id: 1005, name: '温度传感器', value: '42.8', unit: '°C', status: 'warning' },
                  { id: 1006, name: '湿度传感器', value: '72', unit: '%', status: 'normal' },
                  { id: 1007, name: '压力传感器', value: '1.45', unit: 'MPa', status: 'warning' },
                  { id: 1008, name: '振动传感器', value: '0.22', unit: 'g', status: 'normal' }
                ]
              }
            ],
            cameras: [
              {
                id: 'cam-1-1',
                name: 'A区域前端摄像头',
                type: '高清摄像头',
                status: 'online',
                url: 'https://media.w3.org/2010/05/sintel/trailer.mp4',
                rtsp_url: 'rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4'
              },
              {
                id: 'cam-1-2',
                name: 'A区域后端摄像头',
                type: '高清摄像头',
                status: 'online',
                url: 'https://media.w3.org/2010/05/bunny/movie.mp4',
                rtsp_url: 'rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4'
              }
            ]
          },
          {
            id: 2,
            name: '造桥机B区域监控系统',
            status: 'normal',
            gateways: [
              {
                id: 201,
                name: '网关3号',
                status: 'normal',
                sensors: [
                  { id: 2001, name: '温度传感器', value: '35.2', unit: '°C', status: 'normal' },
                  { id: 2002, name: '湿度传感器', value: '62', unit: '%', status: 'normal' },
                  { id: 2003, name: '压力传感器', value: '0.98', unit: 'MPa', status: 'normal' },
                  { id: 2004, name: '振动传感器', value: '0.12', unit: 'g', status: 'normal' }
                ]
              }
            ],
            cameras: [
              {
                id: 'cam-2-1',
                name: 'B区域前端摄像头',
                type: '高清摄像头',
                status: 'online',
                url: 'https://media.w3.org/2010/05/video/movie_300.mp4',
                rtsp_url: 'rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4'
              }
            ]
          },
          {
            id: 3,
            name: '造桥机C区域监控系统',
            status: 'offline',
            gateways: [
              {
                id: 301,
                name: '网关4号',
                status: 'offline',
                sensors: [
                  { id: 3001, name: '温度传感器', value: '0', unit: '°C', status: 'danger' },
                  { id: 3002, name: '湿度传感器', value: '0', unit: '%', status: 'danger' },
                  { id: 3003, name: '压力传感器', value: '0', unit: 'MPa', status: 'danger' },
                  { id: 3004, name: '振动传感器', value: '0', unit: 'g', status: 'danger' }
                ]
              }
            ],
            cameras: [
              {
                id: 'cam-3-1',
                name: 'C区域摄像头',
                type: '高清摄像头',
                status: 'offline',
                url: '',
                rtsp_url: ''
              }
            ]
          },
          {
            id: 4,
            name: '造桥机D区域监控系统',
            status: 'warning',
            gateways: [
              {
                id: 401,
                name: '网关5号',
                status: 'warning',
                sensors: [
                  { id: 4001, name: '温度传感器', value: '39.8', unit: '°C', status: 'warning' },
                  { id: 4002, name: '湿度传感器', value: '78', unit: '%', status: 'warning' },
                  { id: 4003, name: '压力传感器', value: '1.32', unit: 'MPa', status: 'normal' },
                  { id: 4004, name: '振动传感器', value: '0.28', unit: 'g', status: 'warning' }
                ]
              }
            ],
            cameras: [
              {
                id: 'cam-4-1',
                name: 'D区域左侧摄像头',
                type: '高清摄像头',
                status: 'warning',
                url: 'https://media.w3.org/2010/05/sintel/trailer.mp4',
                rtsp_url: 'rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4'
              },
              {
                id: 'cam-4-2',
                name: 'D区域右侧摄像头',
                type: '高清摄像头',
                status: 'warning',
                url: 'https://media.w3.org/2010/05/bunny/movie.mp4',
                rtsp_url: 'rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4'
              }
            ]
          }
        ];

        // 计算并输出设备状态统计
        const total = this.deviceList.length;
        const online = this.deviceList.filter(d =>
          d.status === 'normal' || d.status === 'online').length;
        const warning = this.deviceList.filter(d =>
          d.status === 'warning').length;
        const offline = this.deviceList.filter(d =>
          d.status === 'offline').length;

        console.log('设备状态统计:', {
          总数: total,
          在线: online,
          警告: warning,
          离线: offline
        });

        // 不启动自动更新，使用固定数据
        // this.startDeviceDataSimulation();
      }, 800);
    },

    // 返回项目列表
    backToProjectList() {
      console.log('返回项目列表，当前选中项目:', this.selectedProject ? this.selectedProject.name : '无');

      // 检查当前是否处于全屏状态
      const isFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );

      console.log('当前全屏状态:', isFullscreen ? '全屏' : '非全屏');

      // 保存当前全屏状态，以便在返回后恢复
      this.isFullscreen = isFullscreen;

      // 清理样式和状态
      // 移除所有屏幕模式类，但保留全屏相关类
      document.body.classList.remove('large-screen-mode', 'medium-screen-mode', 'small-screen-mode', 'screen-mode');
      document.documentElement.classList.remove('large-screen-mode', 'medium-screen-mode', 'small-screen-mode', 'screen-mode');

      // 如果不是全屏状态，则移除全屏相关类
      if (!isFullscreen) {
        document.body.classList.remove('fullscreen-mode', 'fullscreen-active');
        document.documentElement.classList.remove('fullscreen-mode', 'fullscreen-active');
      }

      // 恢复背景色，但如果是全屏状态则保持滚动条隐藏
      document.body.style.backgroundColor = '';
      document.documentElement.style.backgroundColor = '';

      if (!isFullscreen) {
        document.body.style.overflow = '';
        document.documentElement.style.overflow = '';
      }

      // 清除详情视图容器样式
      const detailViewContainer = document.querySelector('.detail-view-container');
      if (detailViewContainer) {
        detailViewContainer.style.backgroundColor = '';
        detailViewContainer.style.zIndex = '';
        detailViewContainer.style.position = '';
      }

      // 清除项目详情视图样式
      const projectDetailView = document.querySelector('.project-detail-view');
      if (projectDetailView) {
        projectDetailView.style.zIndex = '';
        projectDetailView.style.position = '';
      }

      // 恢复屏幕容器样式
      const screenContainer = document.querySelector('.screen-container');
      if (screenContainer) {
        screenContainer.style.backgroundColor = '';
      }

      // 重置状态
      this.selectedProject = null;
      this.deviceList = [];
      this.gatewayList = [];
      this.cameraList = [];

      // 恢复URL参数
      const query = { ...this.$route.query };
      if (query.projectId) {
        delete query.projectId;
      }

      // 如果之前有视图状态，则保留
      if (this.viewMode) {
        query.view = this.viewMode;
      }

      // 如果当前是全屏状态，添加全屏参数
      if (isFullscreen) {
        query.fullscreen = 'true';
      }

      // 更新URL但不触发路由变化 - 修复catch问题
      try {
        const routerPromise = this.$router.replace({ query });
        if (routerPromise && typeof routerPromise.catch === 'function') {
          routerPromise.catch(err => {
            if (err && err.name !== 'NavigationDuplicated') {
              console.error('路由更新错误:', err);
            }
          });
        }
      } catch (error) {
        console.error('更新路由时出错:', error);
      }

      // 重新初始化星光背景
      setTimeout(() => {
        this.initStarsBackground();
        // 触发窗口大小变化事件，确保地图正确渲染
        window.dispatchEvent(new Event('resize'));

        // 如果之前是全屏状态，确保返回后也是全屏状态
        if (isFullscreen && !document.fullscreenElement) {
          console.log('尝试恢复全屏状态');
          this.toggleFullScreen();
        }
      }, 300);

      console.log('已清除选中项目，当前状态:', {
        selectedProject: this.selectedProject,
        viewMode: this.viewMode,
        isFullscreen: isFullscreen
      });
    },

    // 切换全屏模式
    toggleFullScreen() {
      console.log('切换全屏模式，当前状态:', this.isFullscreen ? '全屏' : '非全屏');

      if (!document.fullscreenElement &&
          !document.mozFullScreenElement &&
          !document.webkitFullscreenElement &&
          !document.msFullscreenElement) {
        // 进入全屏模式
        const element = this.$refs.screenContainer;
        if (!element) {
          console.error('找不到屏幕容器元素，无法进入全屏模式');
          return;
        }

        console.log('尝试进入全屏模式');
        try {
          if (element.requestFullscreen) {
            element.requestFullscreen();
          } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
          } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
          } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
          }

          // 添加全屏相关类
          document.body.classList.add('fullscreen-mode', 'fullscreen-active');
          document.documentElement.classList.add('fullscreen-mode', 'fullscreen-active');

          // 隐藏滚动条
          document.body.style.overflow = 'hidden';
          document.documentElement.style.overflow = 'hidden';

          // 确保侧边栏完全隐藏 - 更强力的隐藏方式
          const sidebarEl = document.querySelector('.sidebar-container');
          if (sidebarEl) {
            // 添加类
            sidebarEl.classList.add('hide-in-fullscreen');

            // 直接设置样式
            sidebarEl.style.display = 'none';
            sidebarEl.style.visibility = 'hidden';
            sidebarEl.style.opacity = '0';
            sidebarEl.style.width = '0';
            sidebarEl.style.height = '0';
            sidebarEl.style.overflow = 'hidden';
            sidebarEl.style.position = 'absolute';
            sidebarEl.style.left = '-9999px';
            sidebarEl.style.top = '-9999px';
            sidebarEl.style.zIndex = '-9999';
            sidebarEl.style.transform = 'translateX(-100%)';
            sidebarEl.style.transition = 'none';
            sidebarEl.style.pointerEvents = 'none';
            sidebarEl.style.backgroundColor = 'transparent';
            sidebarEl.style.border = 'none';
            sidebarEl.style.boxShadow = 'none';

            // 隐藏所有子元素
            const sidebarChildren = sidebarEl.querySelectorAll('*');
            sidebarChildren.forEach(child => {
              if (child.style.display !== 'none') {
                child.dataset.originalDisplay = child.style.display;
                child.style.display = 'none';
                child.style.visibility = 'hidden';
                child.style.opacity = '0';
              }
            });
          }

          // 确保主容器占满整个屏幕
          const mainContainer = document.querySelector('.main-container');
          if (mainContainer) {
            mainContainer.style.marginLeft = '0';
            mainContainer.style.width = '100%';
            mainContainer.style.maxWidth = '100vw';
            mainContainer.style.left = '0';
          }

          // 确保布局正确响应全屏变化
          this.$nextTick(() => {
            // 触发窗口大小变化事件，确保模型正确渲染
            window.dispatchEvent(new Event('resize'));
          });
        } catch (error) {
          console.error('进入全屏模式失败:', error);
        }
      } else {
        // 退出全屏模式
        console.log('尝试退出全屏模式');
        try {
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
          }

          // 移除全屏相关类
          document.body.classList.remove('fullscreen-mode', 'fullscreen-active');
          document.documentElement.classList.remove('fullscreen-mode', 'fullscreen-active');

          // 恢复侧边栏显示
          const sidebarEl = document.querySelector('.sidebar-container');
          if (sidebarEl) {
            // 移除类
            sidebarEl.classList.remove('hide-in-fullscreen');

            // 重置样式
            sidebarEl.style.display = '';
            sidebarEl.style.visibility = '';
            sidebarEl.style.opacity = '';
            sidebarEl.style.width = '';
            sidebarEl.style.height = '';
            sidebarEl.style.overflow = '';
            sidebarEl.style.position = '';
            sidebarEl.style.left = '';
            sidebarEl.style.top = '';
            sidebarEl.style.zIndex = '';
            sidebarEl.style.transform = '';
            sidebarEl.style.transition = '';
            sidebarEl.style.pointerEvents = '';
            sidebarEl.style.backgroundColor = '';
            sidebarEl.style.border = '';
            sidebarEl.style.boxShadow = '';

            // 恢复子元素的显示
            const sidebarChildren = sidebarEl.querySelectorAll('*');
            sidebarChildren.forEach(child => {
              if (child.dataset.originalDisplay) {
                child.style.display = child.dataset.originalDisplay;
                child.style.visibility = '';
                child.style.opacity = '';
                delete child.dataset.originalDisplay;
              }
            });
          }

          // 恢复主容器样式
          const mainContainer = document.querySelector('.main-container');
          if (mainContainer) {
            mainContainer.style.marginLeft = '';
            mainContainer.style.width = '';
            mainContainer.style.maxWidth = '';
            mainContainer.style.left = '';
          }

          // 恢复滚动条
          document.body.style.overflow = '';
          document.documentElement.style.overflow = '';

          // 确保布局正确响应全屏变化
          this.$nextTick(() => {
            // 触发窗口大小变化事件，确保模型正确渲染
            window.dispatchEvent(new Event('resize'));
          });
        } catch (error) {
          console.error('退出全屏模式失败:', error);
        }
      }

      // 更新全屏状态
      this.isFullscreen = !this.isFullscreen;
      console.log('全屏状态已更新为:', this.isFullscreen ? '全屏' : '非全屏');
    },

    // 更新当前时间
    updateCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      this.currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 初始化星光背景
    initStarsBackground() {
      const canvas = this.$refs.starsCanvas;
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      const width = window.innerWidth;
      const height = window.innerHeight;

      // 调整canvas尺寸以适应窗口
      canvas.width = width;
      canvas.height = height;

      // 创建两类星星：定点的和流动的
      const staticStars = [];
      const flowingStars = [];
      const staticStarCount = 100; // 增加静态星星数量
      const flowingStarCount = 60; // 增加流动星星数量

      // 创建静态星星
      for (let i = 0; i < staticStarCount; i++) {
        staticStars.push({
          x: Math.random() * width,
          y: Math.random() * height,
          size: Math.random() * 1.8 + 0.4, // 增加星星尺寸
          opacity: Math.random() * 0.6 + 0.15, // 增加不透明度
          pulse: Math.random() * 0.04 + 0.01, // 脉冲速度
          increment: Math.random() * 0.02 // 增量值
        });
      }

      // 创建流动星星 - 水平和垂直方向移动
      for (let i = 0; i < flowingStarCount; i++) {
        // 决定方向，部分水平运动，部分垂直运动
        const isHorizontal = Math.random() > 0.5;
        const direction = Math.random() > 0.5 ? 1 : -1; // 正向或反向

        flowingStars.push({
          x: isHorizontal ? (direction > 0 ? 0 : width) : Math.random() * width,
          y: isHorizontal ? Math.random() * height : (direction > 0 ? 0 : height),
          size: Math.random() * 1.5 + 0.6, // 增加星星尺寸
          opacity: Math.random() * 0.5 + 0.2, // 增加不透明度
          speed: Math.random() * 0.7 + 0.3, // 增加速度
          direction: direction,
          isHorizontal: isHorizontal,
          trail: [] // 拖尾效果
        });
      }

      // 动画函数
      const animate = () => {
        // 清除canvas
        ctx.clearRect(0, 0, width, height);

        // 绘制静态星星（带呼吸效果）
        staticStars.forEach(star => {
          // 计算呼吸效果
          star.increment += star.pulse;
          const breathValue = Math.sin(star.increment) * 0.5 + 0.5;
          const opacity = star.opacity * (0.7 + breathValue * 0.3);

          // 绘制发光效果
          ctx.beginPath();
          const gradient = ctx.createRadialGradient(
            star.x, star.y, 0,
            star.x, star.y, star.size * 3
          );
          gradient.addColorStop(0, `rgba(180, 220, 255, ${opacity * 0.8})`);
          gradient.addColorStop(1, 'rgba(180, 220, 255, 0)');
          ctx.fillStyle = gradient;
          ctx.arc(star.x, star.y, star.size * 3, 0, Math.PI * 2);
          ctx.fill();

          // 绘制星星核心
          ctx.beginPath();
          ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
          ctx.fillStyle = `rgba(220, 240, 255, ${opacity})`; // 更亮的颜色
          ctx.fill();
        });

        // 绘制流动星星
        flowingStars.forEach(star => {
          // 移动星星
          if (star.isHorizontal) {
            star.x += star.speed * star.direction;
            // 如果超出边界，重置到另一边
            if ((star.direction > 0 && star.x > width) ||
                (star.direction < 0 && star.x < 0)) {
              star.x = star.direction > 0 ? 0 : width;
              star.y = Math.random() * height;
            }
          } else {
            star.y += star.speed * star.direction;
            // 如果超出边界，重置到另一边
            if ((star.direction > 0 && star.y > height) ||
                (star.direction < 0 && star.y < 0)) {
              star.y = star.direction > 0 ? 0 : height;
              star.x = Math.random() * width;
            }
          }

          // 添加当前位置到轨迹
          star.trail.push({x: star.x, y: star.y, opacity: star.opacity});
          // 限制轨迹长度
          if (star.trail.length > 6) { // 增加轨迹长度
            star.trail.shift();
          }

          // 绘制轨迹
          for (let i = 0; i < star.trail.length; i++) {
            const point = star.trail[i];
            const trailOpacity = point.opacity * (i / star.trail.length);

            ctx.beginPath();
            ctx.arc(point.x, point.y, star.size * 0.7, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(100, 200, 255, ${trailOpacity * 0.8})`; // 增加亮度
            ctx.fill();
          }

          // 绘制星星
          ctx.beginPath();
          ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
          ctx.fillStyle = `rgba(160, 230, 255, ${star.opacity})`; // 更亮的蓝色
          ctx.fill();

          // 添加光晕效果
          ctx.beginPath();
          const gradient = ctx.createRadialGradient(
            star.x, star.y, 0,
            star.x, star.y, star.size * 2
          );
          gradient.addColorStop(0, `rgba(160, 230, 255, ${star.opacity * 0.7})`);
          gradient.addColorStop(1, 'rgba(160, 230, 255, 0)');
          ctx.fillStyle = gradient;
          ctx.arc(star.x, star.y, star.size * 2, 0, Math.PI * 2);
          ctx.fill();
        });

        // 继续动画
        this.animationFrame = requestAnimationFrame(animate);
      };

      // 启动动画
      animate();
      this.starsInitialized = true;
    },

    // 处理窗口大小变化
    handleResize() {
      // 重新初始化星光背景
      if (this.starsInitialized) {
        cancelAnimationFrame(this.animationFrame);
        this.initStarsBackground();
      }

      // 更新弹窗位置
      this.updatePopupPosition();
    },

    // 启动数据轮询
    startDataPolling() {
      console.log('数据轮询功能已禁用');

      // 下面是已禁用的数据轮询代码
      /*
      // 每10秒更新一次数据
      this.pollingTimer = setInterval(() => {
        // 确保方法存在
        if (typeof this.updateDeviceData === 'function') {
          // 更新设备状态
          this.deviceList = this.deviceList.map(device => this.updateDeviceData(device));
        }

        // 更新连接线状态
        this.updateConnectionLines();
      }, 10000);
      */
    },

    // 启动设备数据模拟更新
    startDeviceDataSimulation() {
      console.log('模拟设备数据更新功能已禁用');

      // 下面是已禁用的模拟更新代码
      /*
      // 每5秒更新一次设备数据
      setInterval(() => {
        this.deviceList = this.deviceList.map(device => {
          // 更新网关状态
          const updatedGateways = device.gateways.map(gateway => {
            // 更新传感器数据
            const updatedSensors = gateway.sensors.map(this.updateSensorData);

            // 根据传感器状态更新网关状态
            const hasWarning = updatedSensors.some(s => s.status === 'warning');
            const hasDanger = updatedSensors.some(s => s.status === 'danger');

            let gatewayStatus = 'online';
            if (hasDanger) gatewayStatus = 'offline';
            else if (hasWarning) gatewayStatus = 'warning';

            return {
              ...gateway,
              status: gatewayStatus,
              sensors: updatedSensors
            };
          });

          // 根据网关状态更新设备状态
          const hasOffline = updatedGateways.some(g => g.status === 'offline');
          const hasWarning = updatedGateways.some(g => g.status === 'warning');

          let deviceStatus = 'online';
          if (hasOffline) deviceStatus = 'offline';
          else if (hasWarning) deviceStatus = 'warning';

          return {
            ...device,
            status: deviceStatus,
            gateways: updatedGateways
          };
        });
      }, 5000);
      */
    },

    // 更新传感器数据
    updateSensorData(sensor) {
      try {
        // 确保 value 是数字
        const currentValue = parseFloat(sensor.value) || 0;

        // 根据传感器类型生成不同范围的随机波动
        let fluctuation = 0;
        switch (sensor.type) {
          case 'temperature':
            fluctuation = (Math.random() - 0.5) * 0.5; // 温度波动小
            break;
          case 'vibration':
            fluctuation = (Math.random() - 0.5) * 2; // 振动波动大
            break;
          case 'strain':
            fluctuation = (Math.random() - 0.5) * 1; // 应变波动中等
            break;
          case 'displacement':
            fluctuation = (Math.random() - 0.5) * 0.2; // 位移波动小
            break;
          default:
            fluctuation = (Math.random() - 0.5) * 1;
        }

        // 计算新值
        let newValue = currentValue + fluctuation;

        // 确保值在合理范围内
        if (sensor.type === 'temperature') {
          newValue = Math.max(-10, Math.min(40, newValue)); // 温度范围 -10°C 到 40°C
        } else if (sensor.type === 'vibration') {
          newValue = Math.max(0, Math.min(100, newValue)); // 振动范围 0 到 100
        } else if (sensor.type === 'strain') {
          newValue = Math.max(-500, Math.min(500, newValue)); // 应变范围 -500 到 500
        } else if (sensor.type === 'displacement') {
          newValue = Math.max(-10, Math.min(10, newValue)); // 位移范围 -10mm 到 10mm
        }

        // 格式化数值，保留2位小数
        return {
          ...sensor,
          value: newValue.toFixed(2),
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        console.error('Error updating sensor data:', error, sensor);
        return sensor; // 返回原始数据，避免错误
      }
    },

    // 显示项目信息
    showProjectInfo(project, event) {
      this.hoveredProject = project;

      // 计算弹窗位置
      const x = event.clientX;
      const y = event.clientY;

      // 确保弹窗不超出屏幕边界
      const popupWidth = 300; // 弹窗宽度
      const popupHeight = 350; // 估计的弹窗高度

      let left = x + 10;
      let top = y + 10;

      // 检查右边界
      if (left + popupWidth > window.innerWidth) {
        left = x - popupWidth - 10;
      }

      // 检查下边界
      if (top + popupHeight > window.innerHeight) {
        top = y - popupHeight - 10;
      }

      this.popupStyle = {
        left: `${left}px`,
        top: `${top}px`
      };
    },

    // 更新弹窗位置
    updatePopupPosition() {
      if (this.hoveredProject && this.selectedPopupProject) {
        const element = this.selectedPopupProject;
        const rect = element.getBoundingClientRect();

        this.popupStyle = {
          left: `${rect.right + 10}px`,
          top: `${rect.top}px`
        };
      }
    },

    // 获取项目图片
    getProjectImage(project) {
      // 如果项目有图片，则使用项目图片
      if (project.image) {
        return project.image;
      }

      // 创建canvas元素
      const canvas = document.createElement('canvas');
      canvas.width = 320;
      canvas.height = 160;
      const ctx = canvas.getContext('2d');

      // 使用项目名称的首字母作为种子生成颜色
      // 注意：虽然我们计算了种子值，但在当前实现中并未直接使用
      // 而是通过项目状态来选择颜色
      if (!project.name) {
        project.name = 'Project';
      }

      // 根据项目状态选择适当的颜色
      let color1, color2;
      switch(project.status) {
        case 'completed':
          color1 = 'rgba(40, 120, 50, 0.9)'; // 深绿色
          color2 = 'rgba(67, 160, 71, 0.75)'; // 浅绿色
          break;
        case 'preparing':
          color1 = 'rgba(100, 40, 120, 0.9)'; // 深紫色
          color2 = 'rgba(142, 36, 170, 0.75)'; // 浅紫色
          break;
        case 'paused':
          color1 = 'rgba(150, 80, 20, 0.9)'; // 深橙色
          color2 = 'rgba(251, 140, 0, 0.75)'; // 浅橙色
          break;
        default: // in-progress
          color1 = 'rgba(20, 60, 120, 0.9)'; // 深蓝色
          color2 = 'rgba(30, 136, 229, 0.75)'; // 浅蓝色
      }

      // 创建简单的对角线渐变背景
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
      gradient.addColorStop(0, color1);
      gradient.addColorStop(1, color2);

      // 绘制背景
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // 添加简单网格
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
      ctx.lineWidth = 0.5;

      // 只绘制几条横线和竖线，减少视觉干扰
      for (let x = 0; x <= canvas.width; x += 40) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
      }

      for (let y = 0; y <= canvas.height; y += 40) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
      }

      // 绘制项目首字母
      if (project.name) {
        ctx.font = 'bold 60px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.fillText(project.name.charAt(0).toUpperCase(), canvas.width / 2, canvas.height / 2);
      }

      return canvas.toDataURL('image/png');
    },

    // 初始化3D地图
    init3DMap() {
      try {
        const container = this.$refs.mapContainer
        if (!container) {
          console.error('Map container not found')
          return
        }

        console.log('初始化3D地图容器', container)

      } catch (error) {
        console.error('初始化3D地图失败', error)
      }
    },

    // 清理Three.js场景
    disposeThreeScene() {
      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame)
      }
    },

    // 修改 updateConnectionLines 方法，确保使用数值而非百分比
    updateConnectionLines() {
      try {
        if (!this.$refs.screenContainer) return;

        const containerRect = this.$refs.screenContainer.getBoundingClientRect();
        const containerWidth = containerRect.width;
        const containerHeight = containerRect.height;

        // 中心点坐标（绝对值）
        const centerX = containerWidth / 2;
        const centerY = containerHeight / 2;

        // 创建连接线
        this.connectionLines = [];

        // 遍历项目列表
        this.projectList.forEach((project, index) => {
          // 获取城市名称
          const cityName = project.location ? project.location.split('市')[0] : null;

          // 如果没有找到城市或城市坐标不存在，跳过
          if (!cityName || !this.cityCoordinates[cityName]) {
            console.log(`跳过项目 ${project.name}，未找到城市坐标:`, cityName);
            return;
          }

          // 获取城市坐标（百分比）
          const cityCoord = this.cityCoordinates[cityName];

          // 将百分比转换为绝对坐标
          const endX = (cityCoord.x / 100) * containerWidth;
          const endY = (cityCoord.y / 100) * containerHeight;

          // 计算控制点
          const controlX = (centerX + endX) / 2;
          const controlY = Math.min(centerY, endY) - 50;

          // 创建路径 - 使用绝对数值而非百分比
          const path = `M ${centerX} ${centerY} Q ${controlX} ${controlY}, ${endX} ${endY}`;

          this.connectionLines.push({
            id: `line-${index}`,
            path: path,
            active: false
          });
        });
      } catch (error) {
        console.error('Error updating connection lines:', error);
        this.connectionLines = [];
      }
    },

    // 添加 updateDeviceData 方法
    updateDeviceData(device) {
      try {
        // 随机更新设备状态
        // 使用概率分布而不是直接从数组中选择
        const randomStatus = Math.random();

        let status = device.status;
        if (randomStatus < 0.05) {
          // 5% 概率设备离线
          status = 'offline';
        } else if (randomStatus < 0.15) {
          // 10% 概率设备警告
          status = 'warning';
        } else {
          // 85% 概率设备在线
          status = 'online';
        }

        // 更新设备数据
        return {
          ...device,
          status,
          lastUpdate: new Date().toISOString()
        };
      } catch (error) {
        console.error('Error updating device data:', error, device);
        return device; // 返回原始数据，避免错误
      }
    },

    // 获取项目数据
    async fetchProjectData() {
      try {
        // 获取项目详情
        const projectResponse = await getProjectDetail(this.projectId)
        if (projectResponse && projectResponse.code === 200) {
          this.projectName = projectResponse.data.name
        }

        // 获取项目下的设备列表
        const deviceResponse = await getDeviceList({
          project_id: this.projectId,
          pageSize: 1000
        })
        if (deviceResponse && deviceResponse.code === 200) {
          this.deviceList = deviceResponse.data || []
        }

        // 获取项目下的网关列表
        const gatewayResponse = await getGatewayList({
          project_id: this.projectId,
          pageSize: 1000
        })
        if (gatewayResponse && gatewayResponse.code === 200) {
          this.gatewayList = gatewayResponse.data || []
        }

        // 获取项目下的摄像头列表
        const cameraResponse = await getCameraList({
          project_id: this.projectId,
          pageSize: 1000
        })
        if (cameraResponse && cameraResponse.code === 200) {
          this.cameraList = cameraResponse.data || []
        }
      } catch (error) {
        console.error('获取项目数据失败:', error)
        this.$message.error('获取项目数据失败，请稍后重试')
      }
    },

    // 设备点击事件
    handleDeviceClick(device) {
      this.currentDevice = device
      this.dialogTitle = '设备详情'
      this.dialogVisible = true
    },

    // 网关点击事件
    handleGatewayClick(gateway) {
      this.currentDevice = gateway
      this.dialogTitle = '网关详情'
      this.dialogVisible = true
    },

    // 摄像头点击事件
    handleCameraClick(camera) {
      this.currentDevice = camera
      this.dialogTitle = '摄像头详情'
      this.dialogVisible = true
    },

    // 对话框关闭
    handleDialogClose() {
      this.currentDevice = null
    },

    // 设备状态
    getStatusType(status) {
      const statusMap = {
        0: 'info',      // 离线
        1: 'success',   // 在线
        2: 'danger',    // 故障
        3: 'warning'    // 维护中
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        0: '离线',
        1: '在线',
        2: '故障',
        3: '维护中'
      }
      return statusMap[status] || '未知'
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },

    // 格式化日期（只有年月日）
    formatDate(dateStr) {
      if (!dateStr) return '未设置'

      const date = new Date(dateStr)
      if (isNaN(date.getTime())) return '未设置'

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')

      return `${year}-${month}-${day}`
    },

    // 格式化相对日期（与当前时间比较）
    formatRelativeDate(dateStr) {
      if (!dateStr) return '未设置'

      const date = new Date(dateStr)
      if (isNaN(date.getTime())) return '未设置'

      const now = new Date()
      const diffDays = Math.floor((date - now) / (1000 * 60 * 60 * 24))

      // 格式化基础日期
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const baseDate = `${year}-${month}-${day}`

      // 附加相对时间信息
      if (diffDays > 0) {
        return `${baseDate} (还有${diffDays}天)`
      } else if (diffDays < 0) {
        return `${baseDate} (已过${Math.abs(diffDays)}天)`
      } else {
        return `${baseDate} (今天)`
      }
    },

    // 获取项目状态文本
    getProjectStatusText(status) {
      const statusTextMap = {
        0: '进行中',
        1: '准备中',
        2: '已完成',
        3: '已暂停'
      }
      return statusTextMap[status] || '未知状态'
    },

    // 获取项目状态颜色
    getProjectStatusColor(status) {
      const statusColorMap = {
        0: '#1E88E5',  // 蓝色 - 进行中
        1: '#8E24AA',  // 紫色 - 准备中
        2: '#43A047',  // 绿色 - 已完成
        3: '#FB8C00'   // 橙色 - 已暂停
      }
      return statusColorMap[status] || '#1E88E5'
    },

    // 显示项目提示
    showProjectTooltip(project, event) {
      this.hoveredProject = project

      // 计算提示框位置
      const x = event.clientX
      const y = event.clientY

      // 确保提示框不超出屏幕边界
      const popupWidth = 280
      const popupHeight = 350

      let left = x + 15
      let top = y + 15

      // 检查右边界
      if (left + popupWidth > window.innerWidth) {
        left = x - popupWidth - 15
      }

      // 检查下边界
      if (top + popupHeight > window.innerHeight) {
        top = y - popupHeight - 15
      }

      this.popupStyle = {
        left: `${left}px`,
        top: `${top}px`
      }
    },

    // 隐藏项目提示
    hideProjectTooltip() {
      this.hoveredProject = null
    },

    // 项目点击事件
    async handleProjectClick(project) {
      try {
        this.selectedProject = project;
        this.showProjectDetail = true;

        console.log('点击项目，开始获取项目详情:', project.id);

        // 获取项目详情
        const projectResponse = await getProjectDetail(project.id);
        if (projectResponse.code === 0) {
          // 更新项目数据
          this.selectedProject = projectResponse.data;
          console.log('获取到项目详情:', projectResponse.data);

          // 更新设备列表 - 如果API返回的设备数据为空，使用模拟数据
          if (projectResponse.data.devices && projectResponse.data.devices.length > 0) {
            this.deviceList = projectResponse.data.devices;
            console.log('使用API返回的设备数据:', this.deviceList.length, '个设备');
          } else {
            console.log('API返回的设备数据为空，使用模拟数据');
            // 调用loadDeviceData方法加载模拟数据
            this.loadDeviceData(project.id);
          }

          // 更新网关列表
          this.gatewayList = [];
          if (this.deviceList && this.deviceList.length > 0) {
            this.deviceList.forEach(device => {
              if (device.gateways) {
                this.gatewayList.push(...device.gateways);
              }
            });
          }

          // 更新摄像头列表
          this.cameraList = [];
          if (this.deviceList && this.deviceList.length > 0) {
            this.deviceList.forEach(device => {
              if (device.cameras) {
                this.cameraList.push(...device.cameras);
              }
            });
          }

          console.log('设备数据更新完成:', {
            devices: this.deviceList.length,
            gateways: this.gatewayList.length,
            cameras: this.cameraList.length
          });

          // 设置背景透明，确保地图背景可见
          document.body.style.backgroundColor = 'transparent';
          document.documentElement.style.backgroundColor = 'transparent';

          // 确保父容器背景透明
          const screenContainer = document.querySelector('.screen-container');
          if (screenContainer) {
            screenContainer.style.backgroundColor = 'transparent';
          }

          // 确保详情视图容器样式正确
          const detailViewContainer = document.querySelector('.detail-view-container');
          if (detailViewContainer) {
            detailViewContainer.style.backgroundColor = 'transparent';
            detailViewContainer.style.zIndex = '100';
            detailViewContainer.style.position = 'relative';
            detailViewContainer.style.width = '100%';
            detailViewContainer.style.height = '100%';
          }

          // 确保项目详情视图样式正确
          const projectDetailView = document.querySelector('.project-detail-view');
          if (projectDetailView) {
            projectDetailView.style.zIndex = '100';
            projectDetailView.style.position = 'relative';
            projectDetailView.style.width = '100%';
            projectDetailView.style.height = '100%';
            projectDetailView.style.backgroundColor = 'transparent';
          }

          // 更新URL查询参数
          this.$router.push({
            query: {
              projectId: project.id,
              projectName: project.name
            }
          });

          // 触发窗口大小变化事件，确保布局正确应用
          setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
          }, 100);
        } else {
          this.$message.error(projectResponse.message || '获取项目详情失败');
        }
      } catch (error) {
        console.error('获取项目详情失败:', error);
        this.$message.error('获取项目详情失败');
      }
    },

    // 处理WebGL错误
    handleWebGLError(error) {
      console.warn('WebGL错误:', error);

      // 显示用户友好的错误提示
      this.$notify({
        title: '3D地图加载失败',
        message: '您的浏览器可能不支持WebGL或已被禁用，将使用2D视图代替。' + (error.details ? ' 详情: ' + error.details : ''),
        type: 'warning',
        duration: 8000,
        position: 'top-right'
      });

      // 可以选择自动切换到列表视图
      if (this.viewMode === 'map') {
        setTimeout(() => {
          console.log('由于WebGL错误，自动切换到列表视图');
          this.viewMode = 'list';
        }, 1000);
      }
    },
  },

  mounted() {
    this.init()
    console.log('screen/index.vue组件已挂载，默认视图模式:', this.viewMode)

    // 初始化星光背景
    this.initStarsBackground()

    // 设置项目卡片的动画延迟
    this.$nextTick(() => {
      const projectCards = document.querySelectorAll('.project-card');
      projectCards.forEach((card, index) => {
        card.style.setProperty('--animation-order', index % 5);
      });
    });

    // 监听窗口大小改变，更新弹窗位置和星光背景
    window.addEventListener('resize', () => {
      this.updatePopupPosition()
      this.initStarsBackground()
    })

    // 监听全屏变化
    document.addEventListener('fullscreenchange', () => {
      this.isFullscreen = !!document.fullscreenElement;
      this.initStarsBackground() // 全屏变化时重新初始化星光背景
    });
    document.addEventListener('webkitfullscreenchange', () => {
      this.isFullscreen = !!document.webkitFullscreenElement;
      this.initStarsBackground()
    });
    document.addEventListener('mozfullscreenchange', () => {
      this.isFullscreen = !!document.mozFullscreenElement;
      this.initStarsBackground()
    });
    document.addEventListener('MSFullscreenChange', () => {
      this.isFullscreen = !!document.msFullscreenElement;
      this.initStarsBackground()
    });

    // 检查URL中是否有项目ID参数
    const projectId = this.$route.query.projectId;
    if (projectId) {
      console.log('URL中包含项目ID参数:', projectId);
    }
  },

  beforeDestroy() {
    // 清除动画和计时器
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
    }
    if (this.timer) {
      clearInterval(this.timer)
    }
    window.removeEventListener('resize', this.updatePopupPosition)

    // 移除全屏监听
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange);
  }
}
</script>

<style lang="scss" scoped>
.screen-container {
  position: relative;
  height: calc(100vh - 84px);
  padding: 5px;
  overflow: auto;
  background: linear-gradient(135deg, #041022 0%, #082040 50%, #051835 100%);
  color: #fff;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */

  &::-webkit-scrollbar {
    display: none;  /* Chrome, Safari, Opera */
  }

  /* 添加精致网格背景 */
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      linear-gradient(rgba(0, 150, 220, 0.06) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 150, 220, 0.06) 1px, transparent 1px),
      linear-gradient(rgba(0, 150, 220, 0.03) 0.5px, transparent 0.5px),
      linear-gradient(90deg, rgba(0, 150, 220, 0.03) 0.5px, transparent 0.5px);
    background-size: 50px 50px, 50px 50px, 10px 10px, 10px 10px;
    background-position: center center;
    z-index: 1;
    pointer-events: none;
  }

  /* 添加光晕效果 */
  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 35%, rgba(0, 150, 255, 0.12) 0%, rgba(0, 150, 255, 0) 50%),
      radial-gradient(circle at 80% 20%, rgba(100, 150, 255, 0.08) 0%, rgba(100, 150, 255, 0) 50%),
      radial-gradient(circle at 40% 80%, rgba(50, 200, 255, 0.1) 0%, rgba(50, 200, 255, 0) 60%);
    z-index: 1;
    pointer-events: none;
  }

  /* 全屏模式样式 */
  &.fullscreen-mode {
    height: 100vh;
    padding: 5px;
    background: linear-gradient(135deg, #041022 0%, #082045 50%, #041835 100%);
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 隐藏滚动条 */
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */

    &::-webkit-scrollbar {
      display: none;  /* Chrome, Safari, Opera */
    }

    .screen-header {
      margin-bottom: 40px;
    }

    .model-section {
      margin-bottom: 40px;
    }
  }

  /* 项目选择区域 */
  .project-selection {
    flex: 1;
    position: relative;
    width: 100%;
    z-index: 5;
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */

    &::-webkit-scrollbar {
      display: none;  /* Chrome, Safari, Opera */
    }

    /* 视图切换按钮 */
    .view-toggle {
      position: absolute;
      top: 20px;
      right: 20px;
      z-index: 10;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 30px;
      padding: 5px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
    }

    /* 项目列表容器 */
    .project-list-container {
      width: 100%;
      height: 100%;
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */

      &::-webkit-scrollbar {
        display: none;  /* Chrome, Safari, Opera */
      }

      .project-list-title {
        text-align: center;
        margin-bottom: 25px;
        margin-top: 20px;
        font-size: 28px;
        color: #ffffff;
        text-shadow: 0 0 10px rgba(0, 150, 255, 0.5);
        position: relative;
        letter-spacing: 2px;

        /* 添加底边装饰 */
        &:after {
          content: '';
          position: absolute;
          left: 50%;
          bottom: -10px;
          width: 120px;
          height: 2px;
          background: linear-gradient(
            90deg,
            rgba(0, 150, 220, 0) 0%,
            rgba(0, 150, 220, 0.7) 50%,
            rgba(0, 150, 220, 0) 100%
          );
          transform: translateX(-50%);
        }
      }

      .project-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 25px;
        padding: 20px;
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */

        &::-webkit-scrollbar {
          display: none;  /* Chrome, Safari, Opera */
        }

        .project-card {
          background: rgba(5, 30, 60, 0.75);
          border-radius: 10px;
          overflow: hidden;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
          transition: all 0.25s ease;
          cursor: pointer;
          border: 1px solid rgba(0, 124, 186, 0.3);
          position: relative;
          animation: floatUp 4s ease-in-out infinite alternate;
          animation-delay: calc(var(--animation-order, 0) * 0.5s); /* 使用CSS变量实现错开动画 */

          /* 添加微妙的光晕边缘 */
          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 170, 255, 0.1);
            opacity: 0;
            transition: opacity 0.3s ease;
          }

          &:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
            border-color: rgba(0, 160, 220, 0.6);
            animation-play-state: paused; /* 悬停时暂停动画 */

            .project-image img {
              transform: scale(1.05);
            }

            &:after {
              opacity: 1;
            }
          }

          .project-image {
            position: relative;
            height: 160px;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: linear-gradient(to bottom,
                rgba(0, 0, 0, 0) 0%,
                rgba(0, 0, 0, 0.5) 100%);
              z-index: 1;
            }

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.4s ease;
            }

            .project-status {
              position: absolute;
              top: 10px;
              right: 10px;
              padding: 4px 10px;
              border-radius: 3px;
              font-size: 12px;
              font-weight: 500;
              color: white;
              z-index: 2;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
              backdrop-filter: blur(2px);
              letter-spacing: 0.5px;

              /* 添加边缘发光效果 */
              &:before {
                content: '';
                position: absolute;
                top: -1px;
                left: -1px;
                right: -1px;
                bottom: -1px;
                border-radius: 4px;
                z-index: -1;
                opacity: 0.5;
              }

              &.in-progress {
                background-color: rgba(30, 136, 229, 0.75);

                &:before {
                  box-shadow: 0 0 8px rgba(30, 136, 229, 0.5);
                }
              }
              &.preparing {
                background-color: rgba(142, 36, 170, 0.75);

                &:before {
                  box-shadow: 0 0 8px rgba(142, 36, 170, 0.5);
                }
              }
              &.completed {
                background-color: rgba(67, 160, 71, 0.75);

                &:before {
                  box-shadow: 0 0 8px rgba(67, 160, 71, 0.5);
                }
              }
              &.paused {
                background-color: rgba(251, 140, 0, 0.75);

                &:before {
                  box-shadow: 0 0 8px rgba(251, 140, 0, 0.5);
                }
              }
            }
          }

          .project-info {
            padding: 15px;

            h3 {
              margin: 0 0 10px;
              font-size: 17px;
              font-weight: 500;
              color: #ffffff;
            }

            .project-account,
            .project-location {
              color: #e0e0e0;
              margin-bottom: 8px;
              display: flex;
              align-items: center;
              font-size: 13px;

              i {
                margin-right: 8px;
                color: rgba(0, 170, 230, 0.8);
              }
            }

            .project-progress {
              margin-top: 12px;

              span {
                display: block;
                margin-bottom: 5px;
                color: #e0e0e0;
                font-size: 13px;
              }

              ::v-deep .el-progress-bar__outer {
                background-color: rgba(255, 255, 255, 0.08);
                border-radius: 2px;
                height: 6px !important;
                box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
                overflow: hidden;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
                  right: 0;
                  height: 1px;
                  background: rgba(255, 255, 255, 0.1);
                }
              }

              ::v-deep .el-progress-bar__inner {
                border-radius: 2px;
                transition: width 0.3s ease;
                position: relative;
                overflow: hidden;

                /* 添加光效 */
                &:after {
                  content: '';
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  background: linear-gradient(
                    90deg,
                    rgba(255, 255, 255, 0) 0%,
                    rgba(255, 255, 255, 0.15) 50%,
                    rgba(255, 255, 255, 0) 100%
                  );
                  transform: translateX(-100%);
                  animation: progressGlow 2s ease-in-out infinite;
                }
              }

              ::v-deep .el-progress__text {
                color: rgba(255, 255, 255, 0.9);
                font-weight: 500;
                font-size: 13px !important;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
              }
            }
          }
        }
      }
    }

    /* 地图容器 */
    .map-container {
      width: 100%;
      height: 100%;
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */

      &::-webkit-scrollbar {
        display: none;  /* Chrome, Safari, Opera */
      }

      .map-card {
        height: calc(100% - 20px);
        margin-top: 20px; /* 减小顶部边距，使地图更靠近中间 */
        background: transparent; // 设置为透明背景
        border: none; // 移除边框
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
        display: flex;
        flex-direction: column;
        justify-content: center; /* 垂直居中 */

        &::-webkit-scrollbar {
          display: none;  /* Chrome, Safari, Opera */
        }

        ::v-deep .el-card__body {
          height: 100%;
          padding: 0;
          background: transparent; // 确保卡片内容区域也是透明的
          -ms-overflow-style: none;  /* IE and Edge */
          scrollbar-width: none;  /* Firefox */
          display: flex;
          align-items: center; /* 垂直居中 */
          justify-content: center; /* 水平居中 */

          &::-webkit-scrollbar {
            display: none;  /* Chrome, Safari, Opera */
          }
        }

        // 移除卡片默认阴影和边框
        ::v-deep.el-card {
          box-shadow: none;
          border: none;
          background-color: transparent;
          -ms-overflow-style: none;  /* IE and Edge */
          scrollbar-width: none;  /* Firefox */

          &::-webkit-scrollbar {
            display: none;  /* Chrome, Safari, Opera */
          }
        }
      }
    }

    /* 项目详情悬浮提示 */
    .project-tooltip {
      position: fixed;
      width: 280px;
      background: rgba(5, 30, 60, 0.85);
      border-radius: 6px;
      padding: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
      z-index: 1000;
      color: #ffffff;
      border: 1px solid rgba(0, 150, 220, 0.3);

      h3 {
        margin-top: 0;
        font-size: 16px;
        color: #ffffff;
        border-bottom: 1px solid rgba(0, 150, 220, 0.2);
        padding-bottom: 8px;
        margin-bottom: 10px;
      }

      p {
        margin: 6px 0;
        font-size: 13px;

        strong {
          color: rgba(150, 200, 255, 0.9);
        }
      }

      .tooltip-footer {
        margin-top: 12px;
        text-align: center;
        font-size: 13px;
        color: rgba(100, 200, 255, 0.9);
        font-weight: 500;
      }
    }
  }

  .map-card {
    height: 100%;
    background: transparent; // 设置为透明背景
    border: none; // 移除边框
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
    display: flex;
    flex-direction: column;
    justify-content: center; /* 垂直居中 */

    &::-webkit-scrollbar {
      display: none;  /* Chrome, Safari, Opera */
    }

    ::v-deep .el-card__body {
      height: 100%; /* 使用100%高度 */
      padding: 0;
      background: transparent; // 确保卡片内容区域也是透明的
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
      display: flex;
      align-items: center; /* 垂直居中 */
      justify-content: center; /* 水平居中 */

      &::-webkit-scrollbar {
        display: none;  /* Chrome, Safari, Opera */
      }
    }

    // 移除卡片默认阴影和边框
    ::v-deep.el-card {
      box-shadow: none;
      border: none;
      background-color: transparent;
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */

      &::-webkit-scrollbar {
        display: none;  /* Chrome, Safari, Opera */
      }
    }
  }
}

/* 星光背景Canvas */
.stars-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
      pointer-events: none;
    }

/* 城市背景和模型背景 */
.model-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 0;
  pointer-events: none;

  /* 添加底部辉光 */
  &:after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100px;
    background: linear-gradient(to top,
      rgba(0, 100, 200, 0.08) 0%,
      rgba(0, 100, 200, 0) 100%);
    pointer-events: none;
  }
}

.city-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  background-size: cover;
  background-position: center;
  opacity: 0.3;
  pointer-events: none;

  /* 添加微妙的动态边缘线 */
  &:before {
    content: '';
    position: absolute;
    left: 50%;
    bottom: 30px;
    width: 70%;
    height: 1px;
    background: linear-gradient(
      90deg,
      rgba(0, 150, 220, 0) 0%,
      rgba(0, 150, 220, 0.2) 50%,
      rgba(0, 150, 220, 0) 100%
    );
    transform: translateX(-50%);
    opacity: 0.7;
    box-shadow: 0 0 15px rgba(0, 150, 220, 0.3);
    animation: subtlePulse 10s infinite alternate;
  }
}

@keyframes subtlePulse {
  0% {
    opacity: 0.5;
    width: 60%;
  }
  100% {
    opacity: 0.9;
    width: 80%;
  }
}

@keyframes floatUp {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-15px);
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes dash {
  to {
    stroke-dashoffset: -1000;
  }
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes progressGlow {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 添加全局样式，在全屏模式下隐藏浏览器滚动条 */
:fullscreen {
  overflow: hidden !important;
  -ms-overflow-style: none !important;  /* IE and Edge */
  scrollbar-width: none !important;  /* Firefox */

  &::-webkit-scrollbar {
    display: none !important;  /* Chrome, Safari, Opera */
  }
}
:-webkit-full-screen {
  overflow: hidden !important;
  -ms-overflow-style: none !important;  /* IE and Edge */
  scrollbar-width: none !important;  /* Firefox */

  &::-webkit-scrollbar {
    display: none !important;  /* Chrome, Safari, Opera */
  }
}
:-moz-full-screen {
  overflow: hidden !important;
  -ms-overflow-style: none !important;  /* IE and Edge */
  scrollbar-width: none !important;  /* Firefox */

  &::-webkit-scrollbar {
    display: none !important;  /* Chrome, Safari, Opera */
  }
}
:-ms-fullscreen {
  overflow: hidden !important;
  -ms-overflow-style: none !important;  /* IE and Edge */
  scrollbar-width: none !important;  /* Firefox */

  &::-webkit-scrollbar {
    display: none !important;  /* Chrome, Safari, Opera */
  }
}

/* 项目详情视图样式 */
.project-detail-view {
  position: relative;
  z-index: 100;
  width: 100%;
  height: 100%;
  background-color: transparent;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */

  &::-webkit-scrollbar {
    display: none;  /* Chrome, Safari, Opera */
  }
}

/* 详情视图容器样式 */
.detail-view-container {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 100;
  background-color: transparent;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */

  &::-webkit-scrollbar {
    display: none;  /* Chrome, Safari, Opera */
  }
}


</style>