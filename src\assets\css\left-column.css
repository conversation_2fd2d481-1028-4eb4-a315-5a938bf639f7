/* 左侧列样式 */
.project-info {
  height: 240px; /* 从280px减少到240px */
  background-color: rgba(0, 30, 60, 0.3);
  backdrop-filter: blur(3px);
}

.project-stats {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 4px; /* 从5px减少到4px */
  padding: 0.2vh 0.2vw; /* 减少内边距 */
  margin-top: 3px; /* 从5px减少到3px */
}

.stat-item {
  background-color: rgba(0, 40, 80, 0.3);
  padding: 0.3vh 0.2vw; /* 从0.4vh减少到0.3vh */
  border-radius: 4px;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  border: 1px solid rgba(0, 168, 255, 0.2);
  min-height: 32px; /* 从36px减少到32px */
  display: flex;
  flex-direction: column;
  justify-content: center;
  backdrop-filter: blur(2px);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 168, 255, 0.5);
  background-color: rgba(0, 50, 100, 0.5);
}

.stat-label {
  font-size: 0.7rem; /* 从0.75rem减少到0.7rem */
  color: #7fdbff;
  margin-bottom: 0.1vh; /* 从0.2vh减少到0.1vh */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-value {
  font-size: 0.8rem; /* 从0.85rem减少到0.8rem */
  color: #fff;
  font-weight: bold;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 项目负责人样式 */
.manager-stat {
  grid-column: span 1;
}

.manager-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 3px;
}

.manager-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(0, 168, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 3px;
  font-size: 14px;
  color: #00a8ff;
  cursor: pointer;
}

.manager-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
}

.manager-status i {
  font-size: 8px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.manager-status.online i {
  color: #00ff9d;
}

/* 项目位置样式 */
.location-stat {
  grid-column: span 1;
}

.location-map {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 3px;
  font-size: 0.65rem;
}

.map-marker {
  color: #ff5555;
  font-size: 12px;
  margin-right: 3px;
  animation: pulse 2s infinite;
  cursor: pointer;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.map-coordinates {
  color: #7fdbff;
  font-size: 0.55rem;
}

/* 时间线样式 */
.timeline-stat {
  grid-column: span 2;
}

.timeline-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 2px; /* 从3px减少到2px */
  padding: 0 2px; /* 从3px减少到2px */
}

.timeline-start, .timeline-end {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 36px; /* 从40px减少到36px */
}

.timeline-date {
  font-size: 0.55rem; /* 从0.6rem减少到0.55rem */
  color: #7fdbff;
}

.timeline-label {
  font-size: 0.55rem; /* 从0.6rem减少到0.55rem */
  color: #fff;
}

.timeline-progress {
  flex: 1;
  position: relative;
  height: 12px; /* 从16px减少到12px */
  margin: 0 4px; /* 从6px减少到4px */
}

.timeline-bar {
  height: 3px;
  background-color: rgba(0, 168, 255, 0.3);
  border-radius: 2px;
  position: relative;
  top: 6px;
}

.timeline-fill {
  height: 100%;
  background: linear-gradient(90deg, #00a8ff, #00ff9d);
  border-radius: 2px;
}

.timeline-now {
  position: absolute;
  bottom: 0;
  color: #00ff9d;
  font-size: 10px;
  transform: translateY(-2px);
  cursor: pointer;
}

/* 设备数量样式 */
.equipment-stat {
  grid-column: span 1;
}

.equipment-icons {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 3px;
  color: #00a8ff;
  cursor: pointer;
}

.equipment-icons i {
  margin: 0 1px;
  font-size: 8px;
}

.equipment-more {
  font-size: 0.6rem;
  color: #7fdbff;
  margin-left: 2px;
}

/* 进度条样式 */
.progress-stat {
  grid-column: span 1;
}

.mini-progress-bar {
  height: 3px;
  background-color: rgba(0, 168, 255, 0.3);
  border-radius: 2px;
  margin-top: 3px;
  overflow: hidden;
}

.mini-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00a8ff, #00ff9d);
  border-radius: 2px;
  position: relative;
}

.mini-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.system-info-chart {
  height: 350px; /* 从380px减少到350px */
}

.environment-monitor {
  height: 400px; /* 调整高度以与3D模型底部对齐 */
  background-color: rgba(0, 30, 60, 0.3);
  backdrop-filter: blur(3px);
}

.environment-data {
  padding: 0;
  height: calc(100% - 30px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 设备标题和导航样式 */
.device-header {
  display: flex;
  padding: 3px;
  margin-bottom: 5px;
  background-color: rgba(0, 40, 80, 0.5);
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 4px;
}

.device-title {
  display: flex;
  align-items: center;
  padding: 3px;
  color: #ffffff;
  font-size: 0.95rem;
  font-weight: bold;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.7);
  flex: 1;
  justify-content: center;
}

.device-title i {
  margin-right: 8px;
  color: #00a8ff;
  font-size: 1.1rem;
}

.device-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.nav-btn {
  width: 24px;
  height: 24px;
  background-color: rgba(0, 60, 120, 0.5);
  border: 1px solid rgba(0, 168, 255, 0.4);
  color: #7fdbff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.7rem;
}

.nav-btn:hover:not(:disabled) {
  background-color: rgba(0, 80, 160, 0.7);
  border-color: rgba(0, 255, 157, 0.6);
  color: #ffffff;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
